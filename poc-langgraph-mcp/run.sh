#!/bin/bash

# Script para executar a POC LangGraph + MCP
# Ativa ambiente virtual e executa aplicação Streamlit

echo "🚀 Iniciando POC LangGraph + MCP..."

# Verifica se ambiente virtual existe
if [ ! -d "venv" ]; then
    echo "❌ Ambiente virtual não encontrado. Criando..."
    python3 -m venv venv
    echo "✅ Ambiente virtual criado!"
fi

# Ativa ambiente virtual
echo "🔄 Ativando ambiente virtual..."
source venv/bin/activate

# Verifica se dependências estão instaladas
if ! python3 -c "import streamlit" 2>/dev/null; then
    echo "📦 Instalando dependências..."
    pip install -r requirements.txt
    echo "✅ Dependências instaladas!"
fi

# Verifica se GROQ_API_KEY está configurada
if ! grep -q "GROQ_API_KEY=gsk_" .env 2>/dev/null; then
    echo "⚠️  ATENÇÃO: Configure sua GROQ_API_KEY no arquivo .env"
    echo "   Edite o arquivo .env e substitua 'your_groq_api_key_here' pela sua chave real"
    echo ""
fi

# Executa aplicação
echo "🌐 Iniciando aplicação Streamlit..."
echo "   Acesse: http://localhost:8501"
echo ""

streamlit run main.py --server.port 8501 --server.address localhost
