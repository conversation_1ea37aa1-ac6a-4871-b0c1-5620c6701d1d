"""
Agente LangGraph com integração MCP
Implementa fluxo ReAct com roteamento condicional para atendimento ao cliente
"""

import os
import asyncio
import logging
from typing import Dict, List, Any, Optional, TypedDict, Annotated
from datetime import datetime

from langchain_groq import ChatGroq
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_core.tools import Tool
from langchain_core.prompts import ChatPromptTemplate

from langgraph.graph import StateGraph, END
from langgraph.graph.message import add_messages
from langgraph.prebuilt import ToolNode
from langgraph.checkpoint.memory import MemorySaver

# MCP imports removidos - usando ferramentas mock para POC
# Em produção: from mcp import ClientSession, StdioServerParameters

# Configuração de logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AgentState(TypedDict):
    """Estado do agente LangGraph"""
    messages: Annotated[List[Any], add_messages]
    query_type: Optional[str]
    customer_id: Optional[str]
    tools_used: List[str]
    confidence_score: float
    next_action: Optional[str]

class CustomerServiceAgent:
    """Agente de atendimento ao cliente usando LangGraph + MCP"""
    
    def __init__(self, groq_api_key: str):
        self.groq_api_key = groq_api_key
        self.llm = ChatGroq(
            api_key=groq_api_key,
            model="llama-3.1-70b-versatile",
            temperature=0.1,
            max_tokens=1000
        )
        self.mcp_tools = []
        self.graph = None
        self.memory = MemorySaver()
        
    def initialize_mcp_client(self):
        """Inicializa cliente MCP e carrega ferramentas"""
        try:
            # Por simplicidade na POC, usa ferramentas mock
            # Em produção, conectaria ao servidor MCP real
            logger.info("Inicializando com ferramentas mock para demonstração")
            self._create_mock_tools()

        except Exception as e:
            logger.error(f"Erro ao inicializar cliente MCP: {e}")
            # Fallback: criar ferramentas mock para demonstração
            self._create_mock_tools()
    
    def _create_langchain_tool(self, mcp_tool, session) -> Tool:
        """Converte ferramenta MCP para LangChain Tool"""
        async def tool_func(**kwargs):
            try:
                result = await session.call_tool(mcp_tool.name, kwargs)
                return result.content[0].text if result.content else "Sem resultado"
            except Exception as e:
                return f"Erro ao executar ferramenta: {e}"
        
        return Tool(
            name=mcp_tool.name,
            description=mcp_tool.description,
            func=lambda **kwargs: asyncio.run(tool_func(**kwargs))
        )
    
    def _create_mock_tools(self):
        """Cria ferramentas mock para demonstração"""
        def search_customer(query: str) -> str:
            return "Cliente encontrado: João Silva (ID: CUST_0001) - Status: Ativo - Segmento: Premium"

        def get_customer_history(customer_id: str) -> str:
            return f"Histórico do cliente {customer_id}: 15 pedidos, R$ 5.430,00 gastos, último contato: 2024-01-15"
        
        def search_knowledge_base(topic: str) -> str:
            kb = {
                "login": "Para problemas de login, verifique senha e limpe o cache",
                "pagamento": "Falhas de pagamento: verifique cartão e limite",
                "entrega": "Atrasos na entrega: consulte código de rastreamento"
            }
            return kb.get(topic.lower(), f"Informação sobre '{topic}' não encontrada na base de conhecimento")
        
        def create_support_ticket(customer_id: str, title: str, description: str, priority: str) -> str:
            return f"Ticket TICK_0101 criado para cliente {customer_id}: {title} (Prioridade: {priority})"
        
        self.mcp_tools = [
            Tool(name="search_customer", description="Busca cliente por ID, nome ou email", func=search_customer),
            Tool(name="get_customer_history", description="Obtém histórico do cliente", func=get_customer_history),
            Tool(name="search_knowledge_base", description="Busca na base de conhecimento", func=search_knowledge_base),
            Tool(name="create_support_ticket", description="Cria ticket de suporte", func=create_support_ticket)
        ]
    
    def _classify_query(self, state: AgentState) -> AgentState:
        """Classifica o tipo de consulta do cliente"""
        last_message = state["messages"][-1].content
        
        classification_prompt = ChatPromptTemplate.from_messages([
            ("system", """Você é um classificador de consultas de atendimento ao cliente.
            Classifique a consulta em uma das categorias:
            - customer_lookup: Busca informações de cliente
            - order_inquiry: Consulta sobre pedidos/histórico
            - technical_support: Suporte técnico/problemas
            - general_inquiry: Consulta geral
            
            Responda apenas com a categoria."""),
            ("human", "{query}")
        ])
        
        response = self.llm.invoke(classification_prompt.format(query=last_message))
        query_type = response.content.strip().lower()
        
        state["query_type"] = query_type
        state["confidence_score"] = 0.8  # Simulado
        
        logger.info(f"Consulta classificada como: {query_type}")
        return state
    
    def _route_query(self, state: AgentState) -> str:
        """Roteia a consulta baseado na classificação"""
        query_type = state.get("query_type", "general_inquiry")
        
        routing_map = {
            "customer_lookup": "search_customer_info",
            "order_inquiry": "get_order_history", 
            "technical_support": "search_knowledge",
            "general_inquiry": "general_response"
        }
        
        next_node = routing_map.get(query_type, "general_response")
        state["next_action"] = next_node
        
        logger.info(f"Roteando para: {next_node}")
        return next_node
    
    def _search_customer_info(self, state: AgentState) -> AgentState:
        """Busca informações do cliente"""
        last_message = state["messages"][-1].content
        
        # Extrai possível ID/nome do cliente da mensagem
        search_tool = next((tool for tool in self.mcp_tools if tool.name == "search_customer"), None)
        
        if search_tool:
            result = search_tool.func(query=last_message)
            state["tools_used"].append("search_customer")
            
            response = f"Informações do cliente encontradas:\n{result}"
            state["messages"].append(AIMessage(content=response))
        else:
            state["messages"].append(AIMessage(content="Ferramenta de busca não disponível"))
        
        return state
    
    def _get_order_history(self, state: AgentState) -> AgentState:
        """Obtém histórico de pedidos do cliente"""
        # Simula extração de customer_id da conversa
        customer_id = "CUST_0001"  # Em produção, extrairia da mensagem
        
        history_tool = next((tool for tool in self.mcp_tools if tool.name == "get_customer_history"), None)
        
        if history_tool:
            result = history_tool.func(customer_id=customer_id)
            state["tools_used"].append("get_customer_history")
            state["customer_id"] = customer_id
            
            response = f"Histórico do cliente:\n{result}"
            state["messages"].append(AIMessage(content=response))
        else:
            state["messages"].append(AIMessage(content="Ferramenta de histórico não disponível"))
        
        return state
    
    def _search_knowledge(self, state: AgentState) -> AgentState:
        """Busca na base de conhecimento"""
        last_message = state["messages"][-1].content
        
        # Extrai tópico principal da mensagem
        topic_extraction_prompt = ChatPromptTemplate.from_messages([
            ("system", "Extraia a palavra-chave principal desta consulta de suporte. Responda apenas com a palavra-chave."),
            ("human", "{query}")
        ])
        
        topic_response = self.llm.invoke(topic_extraction_prompt.format(query=last_message))
        topic = topic_response.content.strip()
        
        kb_tool = next((tool for tool in self.mcp_tools if tool.name == "search_knowledge_base"), None)
        
        if kb_tool:
            result = kb_tool.func(topic=topic)
            state["tools_used"].append("search_knowledge_base")
            
            response = f"Informações da base de conhecimento:\n{result}"
            state["messages"].append(AIMessage(content=response))
        else:
            state["messages"].append(AIMessage(content="Base de conhecimento não disponível"))
        
        return state
    
    def _general_response(self, state: AgentState) -> AgentState:
        """Resposta geral usando LLM"""
        last_message = state["messages"][-1].content
        
        general_prompt = ChatPromptTemplate.from_messages([
            ("system", """Você é um assistente de atendimento ao cliente profissional e prestativo.
            Responda de forma clara, educada e útil. Se não souber algo específico,
            ofereça para buscar mais informações ou criar um ticket de suporte."""),
            ("human", "{query}")
        ])
        
        response = self.llm.invoke(general_prompt.format(query=last_message))
        state["messages"].append(AIMessage(content=response.content))
        
        return state
    
    def _should_continue(self, state: AgentState) -> str:
        """Decide se deve continuar o fluxo ou finalizar"""
        # Lógica simples: finaliza após processar a consulta
        if len(state.get("tools_used", [])) > 0 or state.get("next_action") == "general_response":
            return END
        return "classify_query"
    
    def build_graph(self) -> StateGraph:
        """Constrói o grafo LangGraph"""
        workflow = StateGraph(AgentState)
        
        # Adiciona nós
        workflow.add_node("classify_query", self._classify_query)
        workflow.add_node("search_customer_info", self._search_customer_info)
        workflow.add_node("get_order_history", self._get_order_history)
        workflow.add_node("search_knowledge", self._search_knowledge)
        workflow.add_node("general_response", self._general_response)
        
        # Define ponto de entrada
        workflow.set_entry_point("classify_query")
        
        # Adiciona roteamento condicional
        workflow.add_conditional_edges(
            "classify_query",
            self._route_query,
            {
                "search_customer_info": "search_customer_info",
                "get_order_history": "get_order_history",
                "search_knowledge": "search_knowledge",
                "general_response": "general_response"
            }
        )
        
        # Adiciona edges para finalização
        workflow.add_edge("search_customer_info", END)
        workflow.add_edge("get_order_history", END)
        workflow.add_edge("search_knowledge", END)
        workflow.add_edge("general_response", END)
        
        return workflow.compile(checkpointer=self.memory)
    
    async def process_query(self, query: str, thread_id: str = "default") -> Dict[str, Any]:
        """Processa uma consulta do cliente"""
        if not self.graph:
            self.initialize_mcp_client()
            self.graph = self.build_graph()
        
        # Estado inicial
        initial_state = {
            "messages": [HumanMessage(content=query)],
            "query_type": None,
            "customer_id": None,
            "tools_used": [],
            "confidence_score": 0.0,
            "next_action": None
        }
        
        # Configuração do thread
        config = {"configurable": {"thread_id": thread_id}}
        
        try:
            # Executa o grafo
            result = await self.graph.ainvoke(initial_state, config)
            
            # Extrai resposta final
            final_message = result["messages"][-1].content
            
            return {
                "response": final_message,
                "query_type": result.get("query_type"),
                "tools_used": result.get("tools_used", []),
                "confidence_score": result.get("confidence_score", 0.0),
                "customer_id": result.get("customer_id"),
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Erro ao processar consulta: {e}")
            return {
                "response": "Desculpe, ocorreu um erro ao processar sua consulta. Tente novamente.",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

# Função de conveniência para criar agente
def create_agent(groq_api_key: str) -> CustomerServiceAgent:
    """Cria e retorna uma instância do agente"""
    return CustomerServiceAgent(groq_api_key)
