"""
Aplicação principal da POC LangGraph + MCP
Interface Streamlit para demonstração interativa
"""

import os
import asyncio
import streamlit as st
import logging
from datetime import datetime
from typing import Dict, Any
import json

from dotenv import load_dotenv
from langgraph_agent import create_agent

# Configuração de logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Carrega variáveis de ambiente
load_dotenv()

# Configuração da página
st.set_page_config(
    page_title="POC LangGraph + MCP",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="expanded"
)

# CSS customizado
st.markdown("""
<style>
    .main-header {
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        padding: 1rem;
        border-radius: 10px;
        color: white;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-card {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 8px;
        border-left: 4px solid #667eea;
        margin: 0.5rem 0;
    }
    .chat-message {
        padding: 1rem;
        border-radius: 10px;
        margin: 0.5rem 0;
    }
    .user-message {
        background: #e3f2fd;
        border-left: 4px solid #2196f3;
    }
    .assistant-message {
        background: #f3e5f5;
        border-left: 4px solid #9c27b0;
    }
    .tool-usage {
        background: #fff3e0;
        border: 1px solid #ff9800;
        border-radius: 5px;
        padding: 0.5rem;
        margin: 0.5rem 0;
        font-size: 0.9em;
    }
</style>
""", unsafe_allow_html=True)

def initialize_session_state():
    """Inicializa estado da sessão"""
    if "messages" not in st.session_state:
        st.session_state.messages = []
    if "agent" not in st.session_state:
        st.session_state.agent = None
    if "conversation_stats" not in st.session_state:
        st.session_state.conversation_stats = {
            "total_queries": 0,
            "tools_used": [],
            "query_types": [],
            "avg_confidence": 0.0
        }

def create_agent_instance():
    """Cria instância do agente"""
    groq_api_key = os.getenv("GROQ_API_KEY")
    
    if not groq_api_key:
        st.error("⚠️ GROQ_API_KEY não encontrada. Configure no arquivo .env")
        st.stop()
    
    try:
        agent = create_agent(groq_api_key)
        return agent
    except Exception as e:
        st.error(f"Erro ao criar agente: {e}")
        st.stop()

def display_header():
    """Exibe cabeçalho da aplicação"""
    st.markdown("""
    <div class="main-header">
        <h1>🤖 POC LangGraph + MCP</h1>
        <p>Demonstração de Agente Inteligente para Atendimento ao Cliente</p>
    </div>
    """, unsafe_allow_html=True)

def display_sidebar():
    """Exibe barra lateral com informações e configurações"""
    with st.sidebar:
        st.header("📊 Informações da POC")
        
        # Arquitetura
        st.subheader("🏗️ Arquitetura")
        st.markdown("""
        **Fluxo de Dados:**
        1. **Classificação** - Identifica tipo de consulta
        2. **Roteamento** - Decide ferramentas a usar
        3. **Execução** - Chama ferramentas MCP
        4. **Síntese** - Combina resultados
        """)
        
        # Ferramentas MCP disponíveis
        st.subheader("🛠️ Ferramentas MCP")
        tools_info = {
            "search_customer": "Busca dados do cliente",
            "get_customer_history": "Histórico de pedidos",
            "search_knowledge_base": "Base de conhecimento",
            "create_support_ticket": "Criar ticket de suporte"
        }
        
        for tool, description in tools_info.items():
            st.markdown(f"• **{tool}**: {description}")
        
        # Estatísticas da conversa
        st.subheader("📈 Estatísticas")
        stats = st.session_state.conversation_stats
        
        col1, col2 = st.columns(2)
        with col1:
            st.metric("Consultas", stats["total_queries"])
        with col2:
            st.metric("Confiança Média", f"{stats['avg_confidence']:.1%}")
        
        if stats["tools_used"]:
            st.write("**Ferramentas Usadas:**")
            for tool in set(stats["tools_used"]):
                count = stats["tools_used"].count(tool)
                st.write(f"• {tool}: {count}x")
        
        # Botão para limpar conversa
        if st.button("🗑️ Limpar Conversa", type="secondary"):
            st.session_state.messages = []
            st.session_state.conversation_stats = {
                "total_queries": 0,
                "tools_used": [],
                "query_types": [],
                "avg_confidence": 0.0
            }
            st.rerun()

def display_example_queries():
    """Exibe exemplos de consultas"""
    st.subheader("💡 Exemplos de Consultas")
    
    examples = [
        "Buscar informações do cliente João Silva",
        "Qual o histórico de pedidos do cliente CUST_0001?",
        "Estou com problema de login, como resolver?",
        "Como funciona a política de reembolso?",
        "Criar um ticket de suporte para problema de pagamento"
    ]
    
    cols = st.columns(len(examples))
    for i, example in enumerate(examples):
        with cols[i]:
            if st.button(f"📝 {example[:20]}...", key=f"example_{i}", help=example):
                return example
    
    return None

def display_chat_message(message: Dict[str, Any], is_user: bool = True):
    """Exibe mensagem do chat"""
    if is_user:
        st.markdown(f"""
        <div class="chat-message user-message">
            <strong>👤 Você:</strong><br>
            {message['content']}
        </div>
        """, unsafe_allow_html=True)
    else:
        st.markdown(f"""
        <div class="chat-message assistant-message">
            <strong>🤖 Assistente:</strong><br>
            {message['response']}
        </div>
        """, unsafe_allow_html=True)
        
        # Exibe informações técnicas se disponíveis
        if message.get('tools_used'):
            tools_str = ", ".join(message['tools_used'])
            st.markdown(f"""
            <div class="tool-usage">
                🛠️ <strong>Ferramentas utilizadas:</strong> {tools_str}<br>
                📊 <strong>Tipo de consulta:</strong> {message.get('query_type', 'N/A')}<br>
                🎯 <strong>Confiança:</strong> {message.get('confidence_score', 0):.1%}
            </div>
            """, unsafe_allow_html=True)

async def process_user_query(agent, query: str) -> Dict[str, Any]:
    """Processa consulta do usuário"""
    try:
        result = await agent.process_query(query)
        return result
    except Exception as e:
        logger.error(f"Erro ao processar consulta: {e}")
        return {
            "response": f"Erro ao processar consulta: {e}",
            "error": True,
            "timestamp": datetime.now().isoformat()
        }

def update_conversation_stats(result: Dict[str, Any]):
    """Atualiza estatísticas da conversa"""
    stats = st.session_state.conversation_stats
    
    stats["total_queries"] += 1
    
    if result.get("tools_used"):
        stats["tools_used"].extend(result["tools_used"])
    
    if result.get("query_type"):
        stats["query_types"].append(result["query_type"])
    
    if result.get("confidence_score"):
        # Calcula média móvel da confiança
        current_avg = stats["avg_confidence"]
        new_confidence = result["confidence_score"]
        total_queries = stats["total_queries"]
        
        stats["avg_confidence"] = (current_avg * (total_queries - 1) + new_confidence) / total_queries

def main():
    """Função principal da aplicação"""
    initialize_session_state()
    display_header()
    
    # Cria agente se não existir
    if st.session_state.agent is None:
        with st.spinner("🔄 Inicializando agente..."):
            st.session_state.agent = create_agent_instance()
        st.success("✅ Agente inicializado com sucesso!")
    
    # Layout principal
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.subheader("💬 Chat com o Agente")
        
        # Exibe exemplos de consultas
        example_query = display_example_queries()
        
        # Área de chat
        chat_container = st.container()
        
        # Exibe histórico de mensagens
        with chat_container:
            for message in st.session_state.messages:
                if message["type"] == "user":
                    display_chat_message(message, is_user=True)
                else:
                    display_chat_message(message, is_user=False)
        
        # Input do usuário
        user_input = st.chat_input("Digite sua consulta aqui...")
        
        # Processa input do usuário ou exemplo selecionado
        query_to_process = user_input or example_query
        
        if query_to_process:
            # Adiciona mensagem do usuário
            user_message = {
                "type": "user",
                "content": query_to_process,
                "timestamp": datetime.now().isoformat()
            }
            st.session_state.messages.append(user_message)
            
            # Processa consulta (versão síncrona para compatibilidade Streamlit)
            with st.spinner("🤔 Processando consulta..."):
                try:
                    # Cria novo loop se necessário
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    result = loop.run_until_complete(process_user_query(st.session_state.agent, query_to_process))
                    loop.close()
                except Exception as e:
                    result = {
                        "response": f"Erro ao processar consulta: {e}",
                        "error": True,
                        "timestamp": datetime.now().isoformat()
                    }
            
            # Adiciona resposta do assistente
            assistant_message = {
                "type": "assistant",
                "timestamp": datetime.now().isoformat(),
                **result
            }
            st.session_state.messages.append(assistant_message)
            
            # Atualiza estatísticas
            update_conversation_stats(result)
            
            # Recarrega página para mostrar nova mensagem
            st.rerun()
    
    with col2:
        display_sidebar()

if __name__ == "__main__":
    main()
