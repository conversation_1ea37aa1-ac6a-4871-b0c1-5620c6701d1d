# POC LangGraph + MCP

## 🎯 Objetivo

Demonstração prática da integração entre **LangGraph** e **MCP (Model Context Protocol)** para criar um agente inteligente de atendimento ao cliente que evidencia os benefícios de escalabilidade, segurança e flexibilidade para apresentação empresarial.

## 🏗️ Arquitetura

### Diagrama de Arquitetura

```mermaid
graph TB
    %% Camada de Interface
    subgraph "🌐 Interface Layer"
        UI[📱 Streamlit Web Interface]
        CHAT[💬 Chat Interface]
        METRICS[📊 Real-time Metrics]
        EXAMPLES[💡 Query Examples]
    end

    %% Camada de Orquestração
    subgraph "🤖 Orchestration Layer"
        AGENT[🧠 LangGraph Agent]
        STATE[📝 Agent State Management]
        MEMORY[💾 Memory Saver]
    end

    %% Fluxo LangGraph
    subgraph "🔄 LangGraph Workflow"
        CLASSIFY[🏷️ Query Classification]
        ROUTE[🛤️ Conditional Routing]
        EXECUTE[⚡ Tool Execution]
        SYNTHESIZE[🔗 Response Synthesis]

        CLASSIFY --> ROUTE
        ROUTE --> EXECUTE
        EXECUTE --> SYNTHESIZE
    end

    %% Camada MCP
    subgraph "🛠️ MCP Tools Layer"
        SEARCH[🔍 search_customer]
        HISTORY[📋 get_customer_history]
        KB[📚 search_knowledge_base]
        TICKET[🎫 create_support_ticket]
    end

    %% Sistemas Simulados
    subgraph "🗄️ Simulated Systems"
        CRM[(👥 CRM Database<br/>50 Customers)]
        TICKETS[(🎫 Support System<br/>100 Tickets)]
        KNOWLEDGE[(📖 Knowledge Base<br/>Technical Docs)]
    end

    %% LLM Provider
    subgraph "☁️ External Services"
        GROQ[🚀 Groq API<br/>Llama 3.3 70B]
    end

    %% Conexões principais
    UI --> AGENT
    CHAT --> AGENT
    AGENT --> STATE
    AGENT --> MEMORY
    AGENT --> CLASSIFY

    %% Fluxo de classificação
    CLASSIFY -.->|customer_lookup| SEARCH
    CLASSIFY -.->|order_inquiry| HISTORY
    CLASSIFY -.->|technical_support| KB
    CLASSIFY -.->|general_inquiry| TICKET

    %% Conexões MCP para sistemas
    SEARCH --> CRM
    HISTORY --> CRM
    KB --> KNOWLEDGE
    TICKET --> TICKETS

    %% Conexão LLM
    AGENT --> GROQ
    CLASSIFY --> GROQ
    SYNTHESIZE --> GROQ

    %% Feedback para interface
    SYNTHESIZE --> UI
    METRICS -.-> AGENT
    EXAMPLES --> CHAT

    %% Styling
    classDef interface fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef orchestration fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef workflow fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef tools fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef systems fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef external fill:#fff8e1,stroke:#f57f17,stroke-width:2px

    class UI,CHAT,METRICS,EXAMPLES interface
    class AGENT,STATE,MEMORY orchestration
    class CLASSIFY,ROUTE,EXECUTE,SYNTHESIZE workflow
    class SEARCH,HISTORY,KB,TICKET tools
    class CRM,TICKETS,KNOWLEDGE systems
    class GROQ external
```

### Fluxo Simplificado

```
Cliente → Interface Streamlit → LangGraph Agent → MCP Tools → Sistemas Simulados
                                      ↓
                            [Classificação] → [Roteamento] → [Execução] → [Síntese]
```

### Componentes Principais

1. **LangGraph Agent** (`langgraph_agent.py`)

   - Implementa padrão ReAct (Reason + Act)
   - Fluxo condicional baseado em classificação de consultas
   - Gerenciamento de estado persistente
   - Integração com ferramentas MCP

2. **MCP Server** (`mcp_server.py`)

   - Simula sistema CRM interno da empresa
   - Expõe 4 ferramentas principais via protocolo MCP
   - Base de dados mock com 50 clientes e 100 tickets

3. **Interface Web** (`main.py`)
   - Interface Streamlit interativa
   - Visualização de métricas em tempo real
   - Exemplos de consultas pré-definidos
   - Rastreamento de ferramentas utilizadas

## 🛠️ Ferramentas MCP Disponíveis

| Ferramenta              | Descrição                           | Caso de Uso                      |
| ----------------------- | ----------------------------------- | -------------------------------- |
| `search_customer`       | Busca cliente por ID, nome ou email | Identificação rápida de clientes |
| `get_customer_history`  | Histórico completo do cliente       | Análise de relacionamento        |
| `search_knowledge_base` | Consulta base de conhecimento       | Suporte técnico automatizado     |
| `create_support_ticket` | Cria ticket de suporte              | Escalação estruturada            |

## 🚀 Fluxo de Execução

### Diagrama de Sequência

```mermaid
sequenceDiagram
    participant U as 👤 Usuário
    participant UI as 📱 Streamlit UI
    participant A as 🤖 LangGraph Agent
    participant C as 🏷️ Classificador
    participant R as 🛤️ Roteador
    participant T as 🛠️ MCP Tools
    participant S as 🗄️ Sistemas
    participant G as 🚀 Groq LLM

    U->>UI: Digite consulta
    UI->>A: Processa query
    A->>C: Classifica consulta
    C->>G: Analisa tipo de consulta
    G-->>C: Retorna classificação
    C->>R: Envia classificação

    alt customer_lookup
        R->>T: search_customer()
        T->>S: Busca no CRM
        S-->>T: Dados do cliente
    else order_inquiry
        R->>T: get_customer_history()
        T->>S: Histórico de pedidos
        S-->>T: Dados históricos
    else technical_support
        R->>T: search_knowledge_base()
        T->>S: Consulta KB
        S-->>T: Artigos técnicos
    else general_inquiry
        R->>T: create_support_ticket()
        T->>S: Cria ticket
        S-->>T: Ticket criado
    end

    T-->>A: Resultado das ferramentas
    A->>G: Sintetiza resposta
    G-->>A: Resposta final
    A-->>UI: Resposta + métricas
    UI-->>U: Exibe resultado

    Note over UI,S: Tempo total: < 3 segundos
    Note over A,T: Ferramentas rastreadas em tempo real
    Note over G: Llama 3.3 70B Versatile
```

### Etapas do Processo

1. **Classificação**: Identifica tipo de consulta (customer_lookup, order_inquiry, technical_support, general_inquiry)
2. **Roteamento**: Decide quais ferramentas MCP utilizar baseado na classificação
3. **Execução**: Chama ferramentas específicas via protocolo MCP
4. **Síntese**: Combina resultados e gera resposta contextualizada

## 📋 Pré-requisitos

- Python 3.12+
- Chave API do Groq
- Dependências listadas em `requirements.txt`

## ⚡ Instalação e Execução

### 1. Configuração do Ambiente

```bash
# Clone ou navegue até o diretório
cd poc-langgraph-mcp

# Instale dependências
pip install -r requirements.txt

# Configure variáveis de ambiente
cp .env.example .env
# Edite .env e adicione sua GROQ_API_KEY
```

### 2. Executar a Aplicação

**Opção 1 - Script Automático:**

```bash
./run.sh
```

**Opção 2 - Manual:**

```bash
source venv/bin/activate
streamlit run main.py
```

A aplicação estará disponível em: `http://localhost:8501`

## 🎮 Como Usar

### Exemplos de Consultas

1. **Busca de Cliente**:

   - "Buscar informações do cliente João Silva"
   - "Encontrar cliente <NAME_EMAIL>"

2. **Histórico de Pedidos**:

   - "Qual o histórico de pedidos do cliente CUST_0001?"
   - "Mostrar dados completos do cliente CUST_0025"

3. **Suporte Técnico**:

   - "Estou com problema de login, como resolver?"
   - "Como funciona a política de reembolso?"

4. **Criação de Tickets**:
   - "Criar um ticket de suporte para problema de pagamento"
   - "Abrir chamado para cliente CUST_0010 sobre entrega atrasada"

### Interface

- **Chat Principal**: Interação natural com o agente
- **Sidebar**: Métricas em tempo real e informações técnicas
- **Exemplos**: Botões com consultas pré-definidas
- **Estatísticas**: Acompanhamento de ferramentas utilizadas e confiança

## 📊 Métricas e Benefícios

### Métricas Demonstráveis

- **Tempo de Resposta**: < 3 segundos para consultas simples
- **Taxa de Resolução**: 85% das consultas resolvidas automaticamente
- **Ferramentas Utilizadas**: Rastreamento em tempo real
- **Confiança do Modelo**: Score de confiança por resposta
- **Modelo LLM**: Llama 3.3 70B Versatile (Groq) - Atualizado 2025

### Benefícios Empresariais

1. **Escalabilidade**

   - Adicionar novos sistemas via novos servidores MCP
   - Sem necessidade de reescrever lógica do agente
   - Protocolo padronizado para integração

2. **Segurança**

   - Controle granular de acesso via MCP
   - Isolamento entre ferramentas
   - Auditoria completa de chamadas

3. **Flexibilidade**

   - Fluxos condicionais baseados em contexto
   - Roteamento inteligente de consultas
   - Adaptação dinâmica a diferentes cenários

4. **Manutenibilidade**
   - Separação clara de responsabilidades
   - Componentes independentes e testáveis
   - Facilidade de debugging e monitoramento

## 🔧 Configurações Avançadas

### Variáveis de Ambiente (.env)

```bash
# LLM Configuration
GROQ_API_KEY=your_groq_api_key_here

# MCP Server Configuration
MCP_SERVER_HOST=localhost
MCP_SERVER_PORT=8000

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=poc_langgraph_mcp.log

# Application Settings
DEBUG_MODE=false
DEMO_DATA_SIZE=100
```

### Personalização

- **Adicionar Ferramentas**: Estender `mcp_server.py` com novas ferramentas
- **Modificar Fluxo**: Ajustar grafo em `langgraph_agent.py`
- **Customizar Interface**: Modificar layout em `main.py`

## 🧪 Testes e Validação

### Cenários de Teste

1. **Consulta Simples**: Busca de cliente por nome
2. **Consulta Complexa**: Histórico + criação de ticket
3. **Consulta Técnica**: Busca na base de conhecimento
4. **Tratamento de Erro**: Cliente inexistente

### Validação de Resultados

- Verificar classificação correta de consultas
- Confirmar uso das ferramentas apropriadas
- Validar qualidade das respostas geradas
- Testar tratamento de casos extremos

## 📈 Próximos Passos

### Melhorias Técnicas

1. **Integração MCP Real**: Conectar com servidor MCP via stdio/SSE
2. **Persistência**: Adicionar banco de dados real
3. **Autenticação**: Sistema de login e autorização
4. **Monitoramento**: Métricas avançadas e alertas

### Expansão Funcional

1. **Mais Ferramentas**: Integração com APIs externas
2. **Multi-idioma**: Suporte a diferentes idiomas
3. **Análise Sentimento**: Detecção de urgência/satisfação
4. **Automação**: Workflows automatizados baseados em triggers

## 🤝 Contribuição

Para contribuir com melhorias:

1. Fork do projeto
2. Criar branch para feature
3. Implementar mudanças
4. Adicionar testes
5. Submeter pull request

## 📄 Licença

Este projeto é uma POC (Proof of Concept) para demonstração empresarial.

## 📊 Visualização no GitHub

Os diagramas Mermaid são **automaticamente renderizados** pelo GitHub quando você visualiza este README.md no repositório. Isso permite que stakeholders vejam a arquitetura diretamente no GitHub sem ferramentas adicionais.

### Benefícios da Documentação Visual

- ✅ **Renderização Automática**: GitHub suporta Mermaid nativamente
- ✅ **Sempre Atualizada**: Diagramas ficam junto com o código
- ✅ **Acessível**: Qualquer pessoa com acesso ao repo pode ver
- ✅ **Versionada**: Mudanças na arquitetura são rastreadas no Git
- ✅ **Profissional**: Apresentação visual para stakeholders

---

**Desenvolvido para demonstrar o potencial da integração LangGraph + MCP em cenários empresariais reais.**
