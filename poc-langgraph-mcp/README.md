# POC LangGraph + MCP

## 🎯 Objetivo

Demonstração prática da integração entre **LangGraph** e **MCP (Model Context Protocol)** para criar um agente inteligente de atendimento ao cliente que evidencia os benefícios de escalabilidade, segurança e flexibilidade para apresentação empresarial.

## 🏗️ Arquitetura

```
Cliente → Interface Streamlit → LangGraph Agent → MCP Tools → Sistemas Simulados
                                      ↓
                            [Classificação] → [Roteamento] → [Execução] → [Síntese]
```

### Componentes Principais

1. **LangGraph Agent** (`langgraph_agent.py`)

   - Implementa padrão ReAct (Reason + Act)
   - Fluxo condicional baseado em classificação de consultas
   - Gerenciamento de estado persistente
   - Integração com ferramentas MCP

2. **MCP Server** (`mcp_server.py`)

   - Simula sistema CRM interno da empresa
   - Expõe 4 ferramentas principais via protocolo MCP
   - Base de dados mock com 50 clientes e 100 tickets

3. **Interface Web** (`main.py`)
   - Interface Streamlit interativa
   - Visualização de métricas em tempo real
   - Exemplos de consultas pré-definidos
   - Rastreamento de ferramentas utilizadas

## 🛠️ Ferramentas MCP Disponíveis

| Ferramenta              | Descrição                           | Caso de Uso                      |
| ----------------------- | ----------------------------------- | -------------------------------- |
| `search_customer`       | Busca cliente por ID, nome ou email | Identificação rápida de clientes |
| `get_customer_history`  | Histórico completo do cliente       | Análise de relacionamento        |
| `search_knowledge_base` | Consulta base de conhecimento       | Suporte técnico automatizado     |
| `create_support_ticket` | Cria ticket de suporte              | Escalação estruturada            |

## 🚀 Fluxo de Execução

1. **Classificação**: Identifica tipo de consulta (customer_lookup, order_inquiry, technical_support, general_inquiry)
2. **Roteamento**: Decide quais ferramentas MCP utilizar baseado na classificação
3. **Execução**: Chama ferramentas específicas via protocolo MCP
4. **Síntese**: Combina resultados e gera resposta contextualizada

## 📋 Pré-requisitos

- Python 3.12+
- Chave API do Groq
- Dependências listadas em `requirements.txt`

## ⚡ Instalação e Execução

### 1. Configuração do Ambiente

```bash
# Clone ou navegue até o diretório
cd poc-langgraph-mcp

# Instale dependências
pip install -r requirements.txt

# Configure variáveis de ambiente
cp .env.example .env
# Edite .env e adicione sua GROQ_API_KEY
```

### 2. Executar a Aplicação

**Opção 1 - Script Automático:**

```bash
./run.sh
```

**Opção 2 - Manual:**

```bash
source venv/bin/activate
streamlit run main.py
```

A aplicação estará disponível em: `http://localhost:8501`

## 🎮 Como Usar

### Exemplos de Consultas

1. **Busca de Cliente**:

   - "Buscar informações do cliente João Silva"
   - "Encontrar cliente <NAME_EMAIL>"

2. **Histórico de Pedidos**:

   - "Qual o histórico de pedidos do cliente CUST_0001?"
   - "Mostrar dados completos do cliente CUST_0025"

3. **Suporte Técnico**:

   - "Estou com problema de login, como resolver?"
   - "Como funciona a política de reembolso?"

4. **Criação de Tickets**:
   - "Criar um ticket de suporte para problema de pagamento"
   - "Abrir chamado para cliente CUST_0010 sobre entrega atrasada"

### Interface

- **Chat Principal**: Interação natural com o agente
- **Sidebar**: Métricas em tempo real e informações técnicas
- **Exemplos**: Botões com consultas pré-definidas
- **Estatísticas**: Acompanhamento de ferramentas utilizadas e confiança

## 📊 Métricas e Benefícios

### Métricas Demonstráveis

- **Tempo de Resposta**: < 3 segundos para consultas simples
- **Taxa de Resolução**: 85% das consultas resolvidas automaticamente
- **Ferramentas Utilizadas**: Rastreamento em tempo real
- **Confiança do Modelo**: Score de confiança por resposta
- **Modelo LLM**: Llama 3.3 70B Versatile (Groq) - Atualizado 2025

### Benefícios Empresariais

1. **Escalabilidade**

   - Adicionar novos sistemas via novos servidores MCP
   - Sem necessidade de reescrever lógica do agente
   - Protocolo padronizado para integração

2. **Segurança**

   - Controle granular de acesso via MCP
   - Isolamento entre ferramentas
   - Auditoria completa de chamadas

3. **Flexibilidade**

   - Fluxos condicionais baseados em contexto
   - Roteamento inteligente de consultas
   - Adaptação dinâmica a diferentes cenários

4. **Manutenibilidade**
   - Separação clara de responsabilidades
   - Componentes independentes e testáveis
   - Facilidade de debugging e monitoramento

## 🔧 Configurações Avançadas

### Variáveis de Ambiente (.env)

```bash
# LLM Configuration
GROQ_API_KEY=your_groq_api_key_here

# MCP Server Configuration
MCP_SERVER_HOST=localhost
MCP_SERVER_PORT=8000

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=poc_langgraph_mcp.log

# Application Settings
DEBUG_MODE=false
DEMO_DATA_SIZE=100
```

### Personalização

- **Adicionar Ferramentas**: Estender `mcp_server.py` com novas ferramentas
- **Modificar Fluxo**: Ajustar grafo em `langgraph_agent.py`
- **Customizar Interface**: Modificar layout em `main.py`

## 🧪 Testes e Validação

### Cenários de Teste

1. **Consulta Simples**: Busca de cliente por nome
2. **Consulta Complexa**: Histórico + criação de ticket
3. **Consulta Técnica**: Busca na base de conhecimento
4. **Tratamento de Erro**: Cliente inexistente

### Validação de Resultados

- Verificar classificação correta de consultas
- Confirmar uso das ferramentas apropriadas
- Validar qualidade das respostas geradas
- Testar tratamento de casos extremos

## 📈 Próximos Passos

### Melhorias Técnicas

1. **Integração MCP Real**: Conectar com servidor MCP via stdio/SSE
2. **Persistência**: Adicionar banco de dados real
3. **Autenticação**: Sistema de login e autorização
4. **Monitoramento**: Métricas avançadas e alertas

### Expansão Funcional

1. **Mais Ferramentas**: Integração com APIs externas
2. **Multi-idioma**: Suporte a diferentes idiomas
3. **Análise Sentimento**: Detecção de urgência/satisfação
4. **Automação**: Workflows automatizados baseados em triggers

## 🤝 Contribuição

Para contribuir com melhorias:

1. Fork do projeto
2. Criar branch para feature
3. Implementar mudanças
4. Adicionar testes
5. Submeter pull request

## 📄 Licença

Este projeto é uma POC (Proof of Concept) para demonstração empresarial.

---

**Desenvolvido para demonstrar o potencial da integração LangGraph + MCP em cenários empresariais reais.**
