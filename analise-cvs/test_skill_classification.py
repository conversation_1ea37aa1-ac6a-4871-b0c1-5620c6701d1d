"""
Unit tests for skill context classification system.

This module tests the enhanced skill classification functionality including
context detection, section analysis, and skill-to-position mapping.
"""

import unittest
from datetime import datetime, timedelta
from unittest.mock import Mock, patch
from career_analyzer import CareerAnalyzer
from models import (
    Position, SkillContext, SkillClassification, AnalysisConfig,
    SkillAnalysis, RedFlag, RedFlagType, Severity
)


class TestSkillClassification(unittest.TestCase):
    """Test cases for skill context classification system."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.config = AnalysisConfig(
            gap_threshold_months=3,
            short_position_threshold_months=6,
            recent_experience_years=5,
            mandatory_skills=['Python', 'JavaScript', 'SQL'],
            preferred_skills=['React', 'Docker', 'AWS']
        )
        self.analyzer = CareerAnalyzer(self.config)
        
        # Sample positions for testing
        self.positions = [
            Position(
                title="Senior Software Engineer",
                company="Tech Corp",
                start_date=datetime(2020, 1, 1),
                end_date=datetime(2023, 6, 1),
                is_current=False,
                description="Developed web applications using Python, Django, and React. Led a team of 5 developers.",
                skills_used=["Python", "Django", "React", "PostgreSQL"]
            ),
            Position(
                title="Full Stack Developer",
                company="StartupXYZ",
                start_date=datetime(2023, 7, 1),
                end_date=None,
                is_current=True,
                description="Building microservices with Node.js and React. Working with AWS and Docker containers.",
                skills_used=["JavaScript", "Node.js", "React", "AWS", "Docker"]
            ),
            Position(
                title="Junior Developer",
                company="First Job Inc",
                start_date=datetime(2018, 6, 1),
                end_date=datetime(2019, 12, 31),
                is_current=False,
                description="Learned Java and Spring framework. Built basic CRUD applications.",
                skills_used=["Java", "Spring", "MySQL"]
            )
        ]
    
    def test_analyze_cv_sections_education(self):
        """Test CV section analysis for education section."""
        cv_text = """
        John Doe
        Software Engineer
        
        EDUCATION
        Bachelor of Science in Computer Science
        University of Technology, 2018
        Relevant coursework: Data Structures, Algorithms, Database Systems
        Thesis: Machine Learning Applications in Web Development using Python
        
        EXPERIENCE
        Software Engineer at Tech Corp
        """
        
        sections = self.analyzer._analyze_cv_sections(cv_text)
        
        self.assertIn('education', sections)
        education_section = sections['education']
        self.assertIn('university', education_section['text'])
        self.assertIn('bachelor', education_section['text'])
        self.assertGreater(education_section['keyword_density'], 0)
    
    def test_analyze_cv_sections_experience(self):
        """Test CV section analysis for experience section."""
        cv_text = """
        EXPERIENCE
        Senior Software Engineer - Tech Corp (2020-2023)
        • Developed scalable web applications using Python and Django
        • Led a team of 5 developers in agile environment
        • Implemented CI/CD pipelines using Docker and AWS
        
        EDUCATION
        Computer Science Degree
        """
        
        sections = self.analyzer._analyze_cv_sections(cv_text)
        
        self.assertIn('experience', sections)
        experience_section = sections['experience']
        self.assertIn('developed', experience_section['text'])
        self.assertIn('led', experience_section['text'])
        self.assertGreater(experience_section['keyword_density'], 0)
    
    def test_analyze_cv_sections_certifications(self):
        """Test CV section analysis for certifications section."""
        cv_text = """
        CERTIFICATIONS
        • AWS Certified Solutions Architect - Professional (2023)
        • Certified Kubernetes Administrator (CKA) - 2022
        • Oracle Certified Java Programmer - 2021
        
        SKILLS
        Python, Java, AWS, Kubernetes
        """
        
        sections = self.analyzer._analyze_cv_sections(cv_text)
        
        self.assertIn('certifications', sections)
        cert_section = sections['certifications']
        self.assertIn('aws certified', cert_section['text'])
        self.assertIn('certified', cert_section['text'])
        self.assertGreater(cert_section['keyword_density'], 0)
    
    def test_classify_skill_context_professional(self):
        """Test skill classification for professional context."""
        cv_text = """
        EXPERIENCE
        Senior Software Engineer - Tech Corp (2020-2023)
        • Developed scalable web applications using Python and Django
        • Implemented REST APIs and worked with PostgreSQL databases
        • Led code reviews and mentored junior developers
        """
        
        skills = ["Python", "Django", "PostgreSQL"]
        classifications = self.analyzer.classify_skill_context(skills, cv_text, self.positions)
        
        # Python should be classified as professional
        self.assertEqual(classifications["Python"].context, SkillContext.PROFESSIONAL)
        self.assertGreater(classifications["Python"].confidence, 0.5)
        
        # Django should also be professional
        self.assertEqual(classifications["Django"].context, SkillContext.PROFESSIONAL)
        
        # PostgreSQL should be professional
        self.assertEqual(classifications["PostgreSQL"].context, SkillContext.PROFESSIONAL)
    
    def test_classify_skill_context_academic(self):
        """Test skill classification for academic context."""
        cv_text = """
        EDUCATION
        Master of Science in Computer Science - University of Tech (2016-2018)
        Thesis: "Advanced Machine Learning Algorithms using Python"
        Coursework: Data Structures in C++, Database Systems with SQL
        Academic projects included web development with JavaScript
        
        EXPERIENCE
        Software Engineer - worked with Java and Spring
        """
        
        skills = ["Python", "C++", "SQL", "JavaScript"]
        classifications = self.analyzer.classify_skill_context(skills, cv_text, [])
        
        # Skills mentioned in academic context should be classified as academic
        # Note: This depends on the section analysis working correctly
        academic_skills = [skill for skill, classification in classifications.items() 
                          if classification.context == SkillContext.ACADEMIC]
        
        # At least some skills should be identified as academic
        self.assertGreater(len(academic_skills), 0)
    
    def test_classify_skill_context_certification(self):
        """Test skill classification for certification context."""
        cv_text = """
        CERTIFICATIONS
        • AWS Certified Solutions Architect - demonstrated expertise in AWS services
        • Microsoft Azure Fundamentals - certified in cloud computing
        • Docker Certified Associate - container orchestration skills
        
        EXPERIENCE
        Used various cloud platforms in professional work
        """
        
        skills = ["AWS", "Azure", "Docker"]
        classifications = self.analyzer.classify_skill_context(skills, cv_text, [])
        
        # Skills mentioned in certification context should be classified as certification
        cert_skills = [skill for skill, classification in classifications.items() 
                      if classification.context == SkillContext.CERTIFICATION]
        
        self.assertGreater(len(cert_skills), 0)
    
    def test_skill_variations_matching(self):
        """Test that skill variations are properly matched."""
        variations = self.analyzer._get_skill_variations("javascript")
        
        expected_variations = ["javascript", "js", "node.js", "nodejs", "ecmascript"]
        for variation in expected_variations:
            self.assertIn(variation, variations)
    
    def test_skill_used_in_position_direct_match(self):
        """Test direct skill matching in positions."""
        position = self.positions[0]  # Senior Software Engineer with Python
        
        self.assertTrue(self.analyzer._skill_used_in_position("python", position))
        self.assertTrue(self.analyzer._skill_used_in_position("django", position))
        self.assertTrue(self.analyzer._skill_used_in_position("react", position))
    
    def test_skill_used_in_position_variation_match(self):
        """Test skill variation matching in positions."""
        position = Position(
            title="Frontend Developer",
            company="Web Co",
            start_date=datetime(2022, 1, 1),
            description="Built interactive UIs with JS and React.js framework",
            skills_used=["JavaScript", "React"]
        )
        
        # Should match JavaScript through "JS" variation
        self.assertTrue(self.analyzer._skill_used_in_position("javascript", position))
        
        # Should match React through "React.js" variation
        self.assertTrue(self.analyzer._skill_used_in_position("react", position))
    
    def test_map_skill_to_positions(self):
        """Test skill-to-position mapping."""
        positions_for_python = self.analyzer._map_skill_to_positions("Python", self.positions)
        
        # Python should be mapped to the Senior Software Engineer position
        self.assertIn("Senior Software Engineer", positions_for_python)
        self.assertEqual(len(positions_for_python), 1)
        
        positions_for_react = self.analyzer._map_skill_to_positions("React", self.positions)
        
        # React should be mapped to both positions that use it
        self.assertIn("Senior Software Engineer", positions_for_react)
        self.assertIn("Full Stack Developer", positions_for_react)
        self.assertEqual(len(positions_for_react), 2)
    
    def test_build_enhanced_skill_to_position_mapping(self):
        """Test enhanced skill-to-position mapping with metadata."""
        skills = ["Python", "React", "Java"]
        mapping = self.analyzer.build_enhanced_skill_to_position_mapping(skills, self.positions)
        
        # Check Python mapping
        python_data = mapping["Python"]
        self.assertGreater(len(python_data['positions_used']), 0)
        self.assertGreater(python_data['total_mentions'], 0)
        
        # Check that position info includes required fields
        position_info = python_data['positions_used'][0]
        self.assertIn('title', position_info)
        self.assertIn('company', position_info)
        self.assertIn('mention_count', position_info)
        self.assertIn('context_strength', position_info)
        
        # Check React mapping (should have recent usage)
        react_data = mapping["React"]
        self.assertTrue(react_data['recent_usage'])  # Used in current position
        
        # Check Java mapping (should not have recent usage)
        java_data = mapping["Java"]
        self.assertFalse(java_data['recent_usage'])  # Only used in old position
    
    def test_is_skill_recent(self):
        """Test recent skill detection."""
        # React is used in current position, should be recent
        self.assertTrue(self.analyzer._is_skill_recent("React", self.positions))
        
        # Java is only used in old position, should not be recent
        self.assertFalse(self.analyzer._is_skill_recent("Java", self.positions))
        
        # Python is used in a position that ended in 2023, should be recent
        self.assertTrue(self.analyzer._is_skill_recent("Python", self.positions))
    
    def test_build_skill_analysis(self):
        """Test comprehensive skill analysis building."""
        skills = ["Python", "React", "Java", "Docker", "AWS"]
        cv_text = """
        EXPERIENCE
        Senior Software Engineer - Tech Corp (2020-2023)
        Developed applications using Python and React
        
        Full Stack Developer - StartupXYZ (2023-present)
        Working with React, Docker, and AWS
        
        Junior Developer - First Job (2018-2019)
        Learned Java programming
        """
        
        skill_analysis = self.analyzer.build_skill_analysis(skills, cv_text, self.positions)
        
        # Check that analysis contains expected fields
        self.assertIsInstance(skill_analysis.professional_skills, list)
        self.assertIsInstance(skill_analysis.academic_skills, list)
        self.assertIsInstance(skill_analysis.certification_skills, list)
        self.assertIsInstance(skill_analysis.recent_skills, list)
        self.assertIsInstance(skill_analysis.outdated_skills, list)
        self.assertIsInstance(skill_analysis.skill_timeline, dict)
        self.assertIsInstance(skill_analysis.skill_classifications, list)
        
        # Check that recent skills include currently used skills
        self.assertIn("React", skill_analysis.recent_skills)
        self.assertIn("Docker", skill_analysis.recent_skills)
        self.assertIn("AWS", skill_analysis.recent_skills)
        
        # Check that outdated skills include old skills
        self.assertIn("Java", skill_analysis.outdated_skills)
    
    def test_generate_skill_red_flags_missing_mandatory(self):
        """Test red flag generation for missing mandatory skills."""
        skill_analysis = SkillAnalysis(
            professional_skills=["Python", "React"],
            academic_skills=["Java"],
            certification_skills=["AWS"]
        )
        
        mandatory_skills = ["Python", "JavaScript", "SQL"]  # JavaScript and SQL are missing
        preferred_skills = ["React", "Docker"]
        
        red_flags = self.analyzer.generate_skill_red_flags(skill_analysis, mandatory_skills, preferred_skills)
        
        # Should generate red flags for missing mandatory skills
        missing_skill_flags = [flag for flag in red_flags if flag.type == RedFlagType.MISSING_REQUIRED_SKILL]
        self.assertGreater(len(missing_skill_flags), 0)
        
        # Check that JavaScript and SQL are flagged as missing
        missing_skills = [flag.details['skill'] for flag in missing_skill_flags]
        self.assertIn("JavaScript", missing_skills)
        self.assertIn("SQL", missing_skills)
    
    def test_generate_skill_red_flags_academic_only(self):
        """Test red flag generation for academic-only mandatory skills."""
        skill_analysis = SkillAnalysis(
            professional_skills=["Python"],
            academic_skills=["JavaScript", "SQL"],  # These are mandatory but only academic
            certification_skills=[]
        )
        
        mandatory_skills = ["Python", "JavaScript", "SQL"]
        preferred_skills = []
        
        red_flags = self.analyzer.generate_skill_red_flags(skill_analysis, mandatory_skills, preferred_skills)
        
        # Should generate red flags for academic-only mandatory skills
        academic_only_flags = [flag for flag in red_flags if flag.type == RedFlagType.ACADEMIC_ONLY_SKILL]
        self.assertGreater(len(academic_only_flags), 0)
        
        # Check that JavaScript and SQL are flagged as academic-only
        academic_only_skills = [flag.details['skill'] for flag in academic_only_flags]
        self.assertIn("JavaScript", academic_only_skills)
        self.assertIn("SQL", academic_only_skills)
    
    def test_generate_skill_red_flags_outdated(self):
        """Test red flag generation for outdated skills."""
        skill_analysis = SkillAnalysis(
            professional_skills=["Python", "JavaScript"],
            recent_skills=["Python"],  # JavaScript is not recent
            outdated_skills=["JavaScript"]  # JavaScript is outdated but required
        )
        
        mandatory_skills = ["Python", "JavaScript"]
        preferred_skills = []
        
        red_flags = self.analyzer.generate_skill_red_flags(skill_analysis, mandatory_skills, preferred_skills)
        
        # Should generate red flags for outdated required skills
        outdated_flags = [flag for flag in red_flags if flag.type == RedFlagType.OUTDATED_SKILL]
        self.assertGreater(len(outdated_flags), 0)
        
        # Check that JavaScript is flagged as outdated
        outdated_skills = [flag.details['skill'] for flag in outdated_flags]
        self.assertIn("JavaScript", outdated_skills)
    
    def test_assess_skill_context_strength(self):
        """Test skill context strength assessment."""
        position = Position(
            title="Senior Python Developer",
            company="Tech Corp",
            description="Developed and implemented Python applications. Led Python development team.",
            skills_used=["Python", "Django"]
        )
        
        # Python should have high context strength (in title, description, and skills)
        strength = self.analyzer._assess_skill_context_strength("python", position)
        self.assertGreater(strength, 0.7)
        
        # A skill not mentioned should have low strength
        strength_low = self.analyzer._assess_skill_context_strength("java", position)
        self.assertEqual(strength_low, 0.0)
    
    def test_extract_proficiency_indicators(self):
        """Test proficiency indicator extraction."""
        position = Position(
            title="Senior Python Developer",
            description="Expert in Python development with advanced Django skills. Led team using Python."
        )
        
        indicators = self.analyzer._extract_proficiency_indicators("python", position)
        
        # Should extract proficiency indicators
        self.assertGreater(len(indicators), 0)
        
        # Should include expert-level indicators
        expert_indicators = [ind for ind in indicators if 'expert' in ind]
        self.assertGreater(len(expert_indicators), 0)
    
    def test_find_skill_mentions(self):
        """Test skill mention finding in text."""
        text = "I have experience with Python programming and python scripting. Also used Python3."
        mentions = self.analyzer._find_skill_mentions("python", text.lower())
        
        # Should find multiple mentions
        self.assertGreater(len(mentions), 0)
        
        # Check that positions are correct
        for start, end in mentions:
            mentioned_text = text.lower()[start:end]
            self.assertEqual(mentioned_text, "python")
    
    def test_is_whole_word(self):
        """Test whole word detection."""
        text = "I use JavaScript and Java programming languages"
        
        # "Java" at position should be detected as whole word
        java_pos = text.lower().find("java")
        self.assertTrue(self.analyzer._is_whole_word("java", text.lower(), java_pos))
        
        # "Java" within "JavaScript" should not be detected as whole word
        javascript_pos = text.lower().find("javascript")
        java_in_js_pos = javascript_pos  # "java" is at the start of "javascript"
        self.assertFalse(self.analyzer._is_whole_word("java", text.lower(), java_in_js_pos))


class TestSkillClassificationEdgeCases(unittest.TestCase):
    """Test edge cases for skill classification."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.config = AnalysisConfig()
        self.analyzer = CareerAnalyzer(self.config)
    
    def test_empty_skills_list(self):
        """Test handling of empty skills list."""
        classifications = self.analyzer.classify_skill_context([], "Some CV text", [])
        self.assertEqual(len(classifications), 0)
    
    def test_empty_cv_text(self):
        """Test handling of empty CV text."""
        skills = ["Python", "Java"]
        classifications = self.analyzer.classify_skill_context(skills, "", [])
        
        # Should return classifications with UNCLEAR context
        for skill, classification in classifications.items():
            self.assertEqual(classification.context, SkillContext.UNCLEAR)
            self.assertLess(classification.confidence, 0.5)
    
    def test_no_positions(self):
        """Test handling when no positions are provided."""
        skills = ["Python", "Java"]
        cv_text = "I know Python and Java programming"
        classifications = self.analyzer.classify_skill_context(skills, cv_text, [])
        
        # Should still work but with limited position mapping
        for skill, classification in classifications.items():
            self.assertEqual(len(classification.positions_used), 0)
            self.assertFalse(classification.is_recent)
    
    def test_skill_not_in_cv(self):
        """Test handling of skills not mentioned in CV."""
        skills = ["COBOL", "Fortran"]  # Unlikely to be in modern CV
        cv_text = "I am a Python developer with React experience"
        classifications = self.analyzer.classify_skill_context(skills, cv_text, [])
        
        # Should return UNCLEAR context with low confidence
        for skill, classification in classifications.items():
            self.assertEqual(classification.context, SkillContext.UNCLEAR)
            self.assertLess(classification.confidence, 0.5)
    
    def test_case_insensitive_matching(self):
        """Test that skill matching is case insensitive."""
        position = Position(
            title="PYTHON DEVELOPER",
            description="Expert in PYTHON programming and Django framework"
        )
        
        # Should match regardless of case
        self.assertTrue(self.analyzer._skill_used_in_position("python", position))
        self.assertTrue(self.analyzer._skill_used_in_position("PYTHON", position))
        self.assertTrue(self.analyzer._skill_used_in_position("Python", position))


if __name__ == '__main__':
    # Run the tests
    unittest.main(verbosity=2)