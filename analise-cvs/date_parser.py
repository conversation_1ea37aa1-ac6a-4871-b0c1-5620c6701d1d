"""
Date parsing and position extraction utilities for CV analysis.

This module provides comprehensive date parsing capabilities including:
- Multiple date format recognition
- Fuzzy date matching and normalization
- Current position detection
- Position extraction from CV text using regex and NLP patterns
"""

import re
import logging
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Tuple, NamedTuple
from dataclasses import dataclass
from enum import Enum

from models import Position


class DateConfidence(Enum):
    """Confidence levels for date extraction."""
    HIGH = "high"      # Exact format match
    MEDIUM = "medium"  # Fuzzy match with high certainty
    LOW = "low"        # Fuzzy match with low certainty
    NONE = "none"      # No date found


@dataclass
class DateRange:
    """Represents a date range with confidence levels."""
    start_date: Optional[datetime]
    end_date: Optional[datetime]
    start_confidence: DateConfidence
    end_confidence: DateConfidence
    raw_text: str
    is_current: bool = False


@dataclass
class ExtractedPosition:
    """Represents a position extracted from CV text."""
    title: str
    company: str
    date_range: DateRange
    description: str
    section_text: str
    confidence: float


class DateParser:
    """
    Advanced date parser for extracting and normalizing dates from CV text.
    
    Supports multiple date formats, fuzzy matching, and current position detection.
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self._setup_patterns()
    
    def _setup_patterns(self):
        """Initialize regex patterns for date extraction."""
        
        # Month patterns (English and Portuguese)
        self.month_patterns = {
            # English months
            r'\b(?:january|jan)\b': 1, r'\b(?:february|feb)\b': 2, r'\b(?:march|mar)\b': 3,
            r'\b(?:april|apr)\b': 4, r'\b(?:may)\b': 5, r'\b(?:june|jun)\b': 6,
            r'\b(?:july|jul)\b': 7, r'\b(?:august|aug)\b': 8, r'\b(?:september|sep|sept)\b': 9,
            r'\b(?:october|oct)\b': 10, r'\b(?:november|nov)\b': 11, r'\b(?:december|dec)\b': 12,
            
            # Portuguese months
            r'\b(?:janeiro|jan)\b': 1, r'\b(?:fevereiro|fev)\b': 2, r'\b(?:março|mar)\b': 3,
            r'\b(?:abril|abr)\b': 4, r'\b(?:maio|mai)\b': 5, r'\b(?:junho|jun)\b': 6,
            r'\b(?:julho|jul)\b': 7, r'\b(?:agosto|ago)\b': 8, r'\b(?:setembro|set)\b': 9,
            r'\b(?:outubro|out)\b': 10, r'\b(?:novembro|nov)\b': 11, r'\b(?:dezembro|dez)\b': 12,
        }
        
        # Current position indicators
        self.current_indicators = [
            r'\b(?:current|presente|atual|atualmente)\b',
            r'\b(?:present|now|hoje)\b',
            r'\b(?:até o momento|até hoje|até agora)\b',
            r'\b(?:ongoing|em andamento)\b',
            r'\b(?:still working|ainda trabalho)\b'
        ]
        
        # Date format patterns (ordered by specificity)
        self.date_patterns = [
            # Full date formats
            (r'\b(\d{1,2})[\/\-\.](\d{1,2})[\/\-\.](\d{4})\b', 'dmy'),  # DD/MM/YYYY
            (r'\b(\d{1,2})[\/\-\.](\d{1,2})[\/\-\.](\d{2})\b', 'dmy_short'),  # DD/MM/YY
            (r'\b(\d{4})[\/\-\.](\d{1,2})[\/\-\.](\d{1,2})\b', 'ymd'),  # YYYY/MM/DD
            
            # Month/Year formats
            (r'\b(\d{1,2})[\/\-\.](\d{4})\b', 'my'),  # MM/YYYY
            (r'\b(\d{4})[\/\-\.](\d{1,2})\b', 'ym'),  # YYYY/MM
            
            # Year only
            (r'\b(\d{4})\b', 'y'),  # YYYY
            
            # Textual month formats
            (r'\b([a-záêçõ]+)\s+(?:de\s+)?(\d{4})\b', 'month_year'),  # Janeiro 2020, Janeiro de 2020
            (r'\b([a-záêçõ]+)\s+(\d{1,2}),?\s+(\d{4})\b', 'month_day_year'),  # January 15, 2020
        ]
        
        # Position extraction patterns
        self.position_patterns = [
            # Common position title patterns
            r'(?:cargo|posição|função|position|role|title):\s*([^\n\r]+)',
            r'(?:empresa|company|organization):\s*([^\n\r]+)',
            
            # Experience section patterns
            r'(?:experiência|experience|trabalho|work)\s*(?:profissional)?[:\-]?\s*\n(.*?)(?:\n\n|\n[A-Z]|\Z)',
            
            # Timeline patterns
            r'(\d{4})\s*[-–]\s*(\d{4}|presente|current|atual)\s*[:\-]?\s*([^\n]+)',
            r'([a-záêçõ]+\s+\d{4})\s*[-–]\s*([a-záêçõ]+\s+\d{4}|presente|current|atual)\s*[:\-]?\s*([^\n]+)',
        ]
    
    def extract_dates_from_text(self, text: str) -> List[DateRange]:
        """
        Extract all date ranges from text.
        
        Args:
            text: Input text to parse
            
        Returns:
            List of DateRange objects found in the text
        """
        text_lower = text.lower()
        date_ranges = []
        
        # Look for date range patterns (start - end)
        range_patterns = [
            r'(\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4})\s*[-–]\s*(\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4})',
            r'(\d{1,2}[\/\-\.]\d{2,4})\s*[-–]\s*(\d{1,2}[\/\-\.]\d{2,4})',
            r'([a-záêçõ]+\s+\d{4})\s*[-–]\s*([a-záêçõ]+\s+\d{4})',
            r'(\d{4})\s*[-–]\s*(\d{4})',
        ]
        
        for pattern in range_patterns:
            matches = re.finditer(pattern, text_lower, re.IGNORECASE)
            for match in matches:
                start_str, end_str = match.groups()
                
                # Check if end indicates current position
                is_current = self._is_current_indicator(end_str)
                
                start_date = self.normalize_date(start_str)
                end_date = None if is_current else self.normalize_date(end_str)
                
                start_conf = DateConfidence.HIGH if start_date else DateConfidence.NONE
                end_conf = DateConfidence.HIGH if end_date or is_current else DateConfidence.NONE
                
                date_range = DateRange(
                    start_date=start_date,
                    end_date=end_date,
                    start_confidence=start_conf,
                    end_confidence=end_conf,
                    raw_text=match.group(0),
                    is_current=is_current
                )
                date_ranges.append(date_range)
        
        # Look for single dates if no ranges found
        if not date_ranges:
            single_dates = self._extract_single_dates(text)
            date_ranges.extend(single_dates)
        
        return date_ranges
    
    def _extract_single_dates(self, text: str) -> List[DateRange]:
        """Extract individual dates from text."""
        single_dates = []
        text_lower = text.lower()
        
        for pattern, format_type in self.date_patterns:
            matches = re.finditer(pattern, text_lower, re.IGNORECASE)
            for match in matches:
                date_obj = self._parse_date_match(match, format_type)
                if date_obj:
                    date_range = DateRange(
                        start_date=date_obj,
                        end_date=None,
                        start_confidence=DateConfidence.MEDIUM,
                        end_confidence=DateConfidence.NONE,
                        raw_text=match.group(0),
                        is_current=False
                    )
                    single_dates.append(date_range)
        
        return single_dates
    
    def normalize_date(self, date_str: str) -> Optional[datetime]:
        """
        Normalize a date string to datetime object.
        
        Args:
            date_str: Date string to normalize
            
        Returns:
            Normalized datetime object or None if parsing fails
        """
        if not date_str or not date_str.strip():
            return None
        
        date_str = date_str.strip().lower()
        
        # Check for current indicators
        if self._is_current_indicator(date_str):
            return None  # Current positions don't have end dates
        
        # Try each date pattern
        for pattern, format_type in self.date_patterns:
            match = re.search(pattern, date_str, re.IGNORECASE)
            if match:
                return self._parse_date_match(match, format_type)
        
        # Try fuzzy matching for month names
        return self._fuzzy_date_match(date_str)
    
    def _parse_date_match(self, match: re.Match, format_type: str) -> Optional[datetime]:
        """Parse a regex match based on format type."""
        try:
            groups = match.groups()
            
            if format_type == 'dmy':  # DD/MM/YYYY
                day, month, year = map(int, groups)
                return datetime(year, month, day)
            
            elif format_type == 'dmy_short':  # DD/MM/YY
                day, month, year = map(int, groups)
                year = 2000 + year if year < 50 else 1900 + year
                return datetime(year, month, day)
            
            elif format_type == 'ymd':  # YYYY/MM/DD
                year, month, day = map(int, groups)
                return datetime(year, month, day)
            
            elif format_type == 'my':  # MM/YYYY
                month, year = map(int, groups)
                return datetime(year, month, 1)
            
            elif format_type == 'ym':  # YYYY/MM
                year, month = map(int, groups)
                return datetime(year, month, 1)
            
            elif format_type == 'y':  # YYYY
                year = int(groups[0])
                return datetime(year, 1, 1)
            
            elif format_type == 'month_year':  # Janeiro 2020
                month_str, year = groups
                month_num = self._get_month_number(month_str)
                if month_num:
                    return datetime(int(year), month_num, 1)
            
            elif format_type == 'month_day_year':  # January 15, 2020
                month_str, day, year = groups
                month_num = self._get_month_number(month_str)
                if month_num:
                    return datetime(int(year), month_num, int(day))
        
        except (ValueError, TypeError) as e:
            self.logger.debug(f"Date parsing error: {e}")
            return None
        
        return None
    
    def _get_month_number(self, month_str: str) -> Optional[int]:
        """Get month number from month name."""
        month_str = month_str.lower().strip()
        
        for pattern, month_num in self.month_patterns.items():
            if re.search(pattern, month_str, re.IGNORECASE):
                return month_num
        
        return None
    
    def _fuzzy_date_match(self, date_str: str) -> Optional[datetime]:
        """Attempt fuzzy matching for dates."""
        # Extract numbers that could be years
        year_matches = re.findall(r'\b(19|20)\d{2}\b', date_str)
        if year_matches:
            year = int(year_matches[0])
            
            # Look for month indicators
            month_num = None
            for pattern, month in self.month_patterns.items():
                if re.search(pattern, date_str, re.IGNORECASE):
                    month_num = month
                    break
            
            if month_num:
                return datetime(year, month_num, 1)
            else:
                return datetime(year, 1, 1)
        
        return None
    
    def _is_current_indicator(self, text: str) -> bool:
        """Check if text indicates current/ongoing position."""
        text_lower = text.lower().strip()
        
        for pattern in self.current_indicators:
            if re.search(pattern, text_lower, re.IGNORECASE):
                return True
        
        return False
    
    def detect_current_position(self, text: str) -> bool:
        """
        Detect if text describes a current position.
        
        Args:
            text: Text to analyze
            
        Returns:
            True if current position indicators are found
        """
        return self._is_current_indicator(text)
    
    def calculate_duration(self, start: datetime, end: Optional[datetime]) -> timedelta:
        """
        Calculate duration between two dates.
        
        Args:
            start: Start date
            end: End date (None for current positions)
            
        Returns:
            Duration as timedelta
        """
        if end is None:
            end = datetime.now()
        
        return end - start
    
    def extract_positions_from_text(self, cv_text: str) -> List[ExtractedPosition]:
        """
        Extract work positions from CV text using regex and NLP patterns.
        
        Args:
            cv_text: Full CV text to analyze
            
        Returns:
            List of extracted positions
        """
        positions = []
        
        # Split text into sections
        sections = self._split_into_sections(cv_text)
        
        # Look for experience sections
        experience_sections = self._find_experience_sections(sections)
        
        for section in experience_sections:
            section_positions = self._extract_positions_from_section(section)
            positions.extend(section_positions)
        
        # Remove duplicates and sort by date
        positions = self._deduplicate_positions(positions)
        positions.sort(key=lambda p: p.date_range.start_date or datetime.min, reverse=True)
        
        return positions
    
    def _split_into_sections(self, text: str) -> List[str]:
        """Split CV text into logical sections."""
        # Common section headers
        section_patterns = [
            r'\n\s*(?:experiência|experience|trabalho|work|employment)\s*(?:profissional)?\s*[:\-]?\s*\n',
            r'\n\s*(?:educação|education|formação|academic)\s*[:\-]?\s*\n',
            r'\n\s*(?:habilidades|skills|competências)\s*[:\-]?\s*\n',
            r'\n\s*(?:certificações|certifications|cursos)\s*[:\-]?\s*\n',
        ]
        
        # Split by section headers
        sections = []
        current_section = text
        
        for pattern in section_patterns:
            parts = re.split(pattern, current_section, flags=re.IGNORECASE)
            if len(parts) > 1:
                sections.extend(parts)
                current_section = parts[-1]
        
        if not sections:
            sections = [text]
        
        return [s.strip() for s in sections if s.strip()]
    
    def _find_experience_sections(self, sections: List[str]) -> List[str]:
        """Identify sections that contain work experience."""
        experience_sections = []
        
        experience_keywords = [
            'experiência', 'experience', 'trabalho', 'work', 'employment',
            'cargo', 'position', 'função', 'role', 'empresa', 'company'
        ]
        
        for section in sections:
            section_lower = section.lower()
            
            # Check if section contains experience keywords
            keyword_count = sum(1 for keyword in experience_keywords 
                              if keyword in section_lower)
            
            # Check if section contains date patterns
            date_count = len(self.extract_dates_from_text(section))
            
            # Score section based on keywords and dates
            score = keyword_count * 2 + date_count
            
            if score >= 3:  # Threshold for experience section
                experience_sections.append(section)
        
        return experience_sections
    
    def _extract_positions_from_section(self, section_text: str) -> List[ExtractedPosition]:
        """Extract individual positions from an experience section."""
        positions = []
        
        # Look for position blocks (separated by double newlines or clear breaks)
        position_blocks = re.split(r'\n\s*\n', section_text)
        
        for block in position_blocks:
            if not block.strip():
                continue
            
            position = self._parse_position_block(block, section_text)
            if position:
                positions.append(position)
        
        return positions
    
    def _parse_position_block(self, block_text: str, section_text: str) -> Optional[ExtractedPosition]:
        """Parse a single position block."""
        lines = [line.strip() for line in block_text.split('\n') if line.strip()]
        
        if len(lines) < 2:
            return None
        
        # Extract dates first
        date_ranges = self.extract_dates_from_text(block_text)
        if not date_ranges:
            return None
        
        date_range = date_ranges[0]  # Use first date range found
        
        # Try to identify title and company
        title, company = self._extract_title_and_company(lines, block_text)
        
        if not title and not company:
            return None
        
        # Calculate confidence based on extracted information
        confidence = self._calculate_position_confidence(title, company, date_range, block_text)
        
        return ExtractedPosition(
            title=title or "Unknown Position",
            company=company or "Unknown Company",
            date_range=date_range,
            description=block_text,
            section_text=section_text,
            confidence=confidence
        )
    
    def _extract_title_and_company(self, lines: List[str], block_text: str) -> Tuple[Optional[str], Optional[str]]:
        """Extract job title and company from position block."""
        title = None
        company = None
        
        # Look for explicit patterns first
        title_patterns = [
            r'(?:cargo|posição|função|position|role|title):\s*([^\n\r]+)',
            r'(?:empresa|company|organization):\s*([^\n\r]+)',
        ]
        
        for pattern in title_patterns:
            match = re.search(pattern, block_text, re.IGNORECASE)
            if match:
                if 'cargo' in pattern or 'position' in pattern or 'role' in pattern:
                    title = match.group(1).strip()
                else:
                    company = match.group(1).strip()
        
        # If not found, use heuristics
        if not title or not company:
            # First line often contains title or company
            if lines:
                first_line = lines[0]
                
                # Check if first line looks like a company (contains common company indicators)
                company_indicators = ['ltd', 'inc', 'corp', 'sa', 'ltda', 'eireli', 'me']
                if any(indicator in first_line.lower() for indicator in company_indicators):
                    if not company:
                        company = first_line
                    if len(lines) > 1 and not title:
                        title = lines[1]
                else:
                    # Assume first line is title
                    if not title:
                        title = first_line
                    if len(lines) > 1 and not company:
                        company = lines[1]
        
        return title, company
    
    def _calculate_position_confidence(self, title: Optional[str], company: Optional[str], 
                                     date_range: DateRange, description: str) -> float:
        """Calculate confidence score for extracted position."""
        confidence = 0.0
        
        # Title confidence
        if title:
            confidence += 0.3
            if len(title) > 5:  # Reasonable title length
                confidence += 0.1
        
        # Company confidence
        if company:
            confidence += 0.3
            if len(company) > 3:  # Reasonable company name length
                confidence += 0.1
        
        # Date confidence
        if date_range.start_confidence == DateConfidence.HIGH:
            confidence += 0.2
        elif date_range.start_confidence == DateConfidence.MEDIUM:
            confidence += 0.1
        
        # Description length (more detailed descriptions are more reliable)
        if len(description) > 100:
            confidence += 0.1
        
        return min(1.0, confidence)
    
    def _deduplicate_positions(self, positions: List[ExtractedPosition]) -> List[ExtractedPosition]:
        """Remove duplicate positions based on similarity."""
        if not positions:
            return positions
        
        unique_positions = []
        
        for position in positions:
            is_duplicate = False
            
            for existing in unique_positions:
                # Check for similarity in title and company
                title_similar = self._strings_similar(position.title, existing.title)
                company_similar = self._strings_similar(position.company, existing.company)
                
                # Check for overlapping dates
                dates_overlap = self._dates_overlap(position.date_range, existing.date_range)
                
                if (title_similar and company_similar) or dates_overlap:
                    is_duplicate = True
                    # Keep the one with higher confidence
                    if position.confidence > existing.confidence:
                        unique_positions.remove(existing)
                        unique_positions.append(position)
                    break
            
            if not is_duplicate:
                unique_positions.append(position)
        
        return unique_positions
    
    def _strings_similar(self, str1: str, str2: str, threshold: float = 0.8) -> bool:
        """Check if two strings are similar using simple similarity metric."""
        if not str1 or not str2:
            return False
        
        str1_lower = str1.lower().strip()
        str2_lower = str2.lower().strip()
        
        if str1_lower == str2_lower:
            return True
        
        # Simple similarity check based on common words
        words1 = set(str1_lower.split())
        words2 = set(str2_lower.split())
        
        if not words1 or not words2:
            return False
        
        intersection = len(words1.intersection(words2))
        union = len(words1.union(words2))
        
        similarity = intersection / union if union > 0 else 0
        return similarity >= threshold
    
    def _dates_overlap(self, range1: DateRange, range2: DateRange) -> bool:
        """Check if two date ranges overlap significantly."""
        if not range1.start_date or not range2.start_date:
            return False
        
        # Get end dates (use current date for ongoing positions)
        end1 = range1.end_date or datetime.now()
        end2 = range2.end_date or datetime.now()
        
        # Check for overlap
        latest_start = max(range1.start_date, range2.start_date)
        earliest_end = min(end1, end2)
        
        if latest_start <= earliest_end:
            overlap_days = (earliest_end - latest_start).days
            
            # Calculate total duration of both ranges
            duration1 = (end1 - range1.start_date).days
            duration2 = (end2 - range2.start_date).days
            
            # Consider significant overlap if > 50% of shorter duration
            min_duration = min(duration1, duration2)
            return overlap_days > (min_duration * 0.5)
        
        return False
    
    def convert_to_position_models(self, extracted_positions: List[ExtractedPosition]) -> List[Position]:
        """
        Convert extracted positions to Position model objects.
        
        Args:
            extracted_positions: List of ExtractedPosition objects
            
        Returns:
            List of Position model objects
        """
        positions = []
        
        for ext_pos in extracted_positions:
            # Calculate duration if dates are available
            duration_months = None
            if ext_pos.date_range.start_date:
                end_date = ext_pos.date_range.end_date or datetime.now()
                duration = end_date - ext_pos.date_range.start_date
                duration_months = max(1, int(duration.days / 30.44))
            
            position = Position(
                title=ext_pos.title,
                company=ext_pos.company,
                start_date=ext_pos.date_range.start_date,
                end_date=ext_pos.date_range.end_date,
                duration_months=duration_months,
                is_current=ext_pos.date_range.is_current,
                description=ext_pos.description,
                skills_used=[]  # Will be populated by skill extraction in other components
            )
            
            positions.append(position)
        
        return positions