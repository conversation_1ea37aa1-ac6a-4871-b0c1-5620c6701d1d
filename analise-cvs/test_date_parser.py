"""
Comprehensive unit tests for date parsing and position extraction utilities.

This module tests all aspects of the DateParser class including:
- Multiple date format recognition
- Fuzzy date matching and normalization
- Current position detection
- Position extraction from CV text
- Edge cases and error handling
"""

import pytest
from datetime import datetime, timedelta
from typing import List

from date_parser import Date<PERSON>ars<PERSON>, DateRange, ExtractedPosition, DateConfidence
from models import Position


class TestDateParser:
    """Test suite for DateParser class."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.parser = DateParser()
    
    def test_initialization(self):
        """Test DateParser initialization."""
        assert self.parser is not None
        assert hasattr(self.parser, 'month_patterns')
        assert hasattr(self.parser, 'current_indicators')
        assert hasattr(self.parser, 'date_patterns')
        assert hasattr(self.parser, 'position_patterns')
    
    # Date normalization tests
    def test_normalize_date_dd_mm_yyyy(self):
        """Test date normalization for DD/MM/YYYY format."""
        test_cases = [
            ("15/03/2020", datetime(2020, 3, 15)),
            ("01/01/2021", datetime(2021, 1, 1)),
            ("31/12/2019", datetime(2019, 12, 31)),
            ("15-03-2020", datetime(2020, 3, 15)),
            ("15.03.2020", datetime(2020, 3, 15)),
        ]
        
        for date_str, expected in test_cases:
            result = self.parser.normalize_date(date_str)
            assert result == expected, f"Failed for {date_str}"
    
    def test_normalize_date_mm_yyyy(self):
        """Test date normalization for MM/YYYY format."""
        test_cases = [
            ("03/2020", datetime(2020, 3, 1)),
            ("12/2021", datetime(2021, 12, 1)),
            ("01/2019", datetime(2019, 1, 1)),
            ("03-2020", datetime(2020, 3, 1)),
            ("03.2020", datetime(2020, 3, 1)),
        ]
        
        for date_str, expected in test_cases:
            result = self.parser.normalize_date(date_str)
            assert result == expected, f"Failed for {date_str}"
    
    def test_normalize_date_yyyy_only(self):
        """Test date normalization for YYYY format."""
        test_cases = [
            ("2020", datetime(2020, 1, 1)),
            ("2021", datetime(2021, 1, 1)),
            ("1995", datetime(1995, 1, 1)),
        ]
        
        for date_str, expected in test_cases:
            result = self.parser.normalize_date(date_str)
            assert result == expected, f"Failed for {date_str}"
    
    def test_normalize_date_textual_months_english(self):
        """Test date normalization for textual months in English."""
        test_cases = [
            ("January 2020", datetime(2020, 1, 1)),
            ("March 2021", datetime(2021, 3, 1)),
            ("December 2019", datetime(2019, 12, 1)),
            ("Jan 2020", datetime(2020, 1, 1)),
            ("Mar 2021", datetime(2021, 3, 1)),
            ("Dec 2019", datetime(2019, 12, 1)),
            ("January 15, 2020", datetime(2020, 1, 15)),
            ("March 10, 2021", datetime(2021, 3, 10)),
        ]
        
        for date_str, expected in test_cases:
            result = self.parser.normalize_date(date_str)
            assert result == expected, f"Failed for {date_str}"
    
    def test_normalize_date_textual_months_portuguese(self):
        """Test date normalization for textual months in Portuguese."""
        test_cases = [
            ("janeiro 2020", datetime(2020, 1, 1)),
            ("março 2021", datetime(2021, 3, 1)),
            ("dezembro 2019", datetime(2019, 12, 1)),
            ("janeiro de 2020", datetime(2020, 1, 1)),
            ("março de 2021", datetime(2021, 3, 1)),
            ("jan 2020", datetime(2020, 1, 1)),
            ("mar 2021", datetime(2021, 3, 1)),
            ("dez 2019", datetime(2019, 12, 1)),
        ]
        
        for date_str, expected in test_cases:
            result = self.parser.normalize_date(date_str)
            assert result == expected, f"Failed for {date_str}"
    
    def test_normalize_date_invalid_formats(self):
        """Test date normalization with invalid formats."""
        invalid_dates = [
            "",
            "   ",
            "invalid",
            "32/13/2020",  # Invalid day/month
            "abc/def/2020",
            "2020/13/01",  # Invalid month
            "not a date",
        ]
        
        for date_str in invalid_dates:
            result = self.parser.normalize_date(date_str)
            assert result is None, f"Should return None for {date_str}"
    
    def test_normalize_date_current_indicators(self):
        """Test that current indicators return None."""
        current_indicators = [
            "current",
            "presente",
            "atual",
            "present",
            "now",
            "ongoing",
            "até o momento",
            "still working",
        ]
        
        for indicator in current_indicators:
            result = self.parser.normalize_date(indicator)
            assert result is None, f"Should return None for current indicator: {indicator}"
    
    # Current position detection tests
    def test_detect_current_position_english(self):
        """Test current position detection in English."""
        current_texts = [
            "Software Engineer at TechCorp (2020 - current)",
            "Working as Developer since 2019 - present",
            "Senior Developer (ongoing)",
            "Lead Engineer - still working",
            "Manager position from 2021 to now",
        ]
        
        for text in current_texts:
            result = self.parser.detect_current_position(text)
            assert result is True, f"Should detect current position in: {text}"
    
    def test_detect_current_position_portuguese(self):
        """Test current position detection in Portuguese."""
        current_texts = [
            "Desenvolvedor na TechCorp (2020 - presente)",
            "Trabalhando como Analista desde 2019 - atual",
            "Gerente de Projetos (até o momento)",
            "Coordenador - ainda trabalho",
            "Posição de Líder de 2021 até hoje",
            "Desenvolvedor (atualmente)",
        ]
        
        for text in current_texts:
            result = self.parser.detect_current_position(text)
            assert result is True, f"Should detect current position in: {text}"
    
    def test_detect_current_position_false_cases(self):
        """Test that non-current positions are not detected as current."""
        non_current_texts = [
            "Software Engineer at TechCorp (2018 - 2020)",
            "Developer from 2019 to 2021",
            "Manager position ended in December 2020",
            "Former Lead Engineer",
            "Previous role as Analyst",
        ]
        
        for text in non_current_texts:
            result = self.parser.detect_current_position(text)
            assert result is False, f"Should not detect current position in: {text}"
    
    # Date range extraction tests
    def test_extract_dates_from_text_simple_ranges(self):
        """Test extraction of simple date ranges."""
        test_cases = [
            ("Software Engineer (01/2020 - 12/2021)", 2),  # Start and end dates
            ("Developer from March 2019 to January 2021", 2),
            ("Manager 2018 - 2020", 2),
            ("Analyst (15/03/2020 - 20/08/2021)", 2),
        ]
        
        for text, expected_count in test_cases:
            ranges = self.parser.extract_dates_from_text(text)
            assert len(ranges) >= 1, f"Should find at least one date range in: {text}"
            # Check that we found meaningful date information
            assert any(r.start_date is not None for r in ranges), f"Should find start date in: {text}"
    
    def test_extract_dates_from_text_current_positions(self):
        """Test extraction of date ranges with current positions."""
        test_cases = [
            "Software Engineer (01/2020 - current)",
            "Developer from March 2019 to present",
            "Manager 2018 - presente",
            "Analyst (15/03/2020 - atual)",
        ]
        
        for text in test_cases:
            ranges = self.parser.extract_dates_from_text(text)
            assert len(ranges) >= 1, f"Should find date range in: {text}"
            # At least one range should be marked as current
            assert any(r.is_current for r in ranges), f"Should detect current position in: {text}"
    
    def test_extract_dates_from_text_no_dates(self):
        """Test extraction from text with no dates."""
        no_date_texts = [
            "Software Engineer with experience in Python",
            "Responsible for team management",
            "Developed web applications",
            "",
            "   ",
        ]
        
        for text in no_date_texts:
            ranges = self.parser.extract_dates_from_text(text)
            # Should return empty list or ranges with no meaningful dates
            if ranges:
                assert all(r.start_date is None and r.end_date is None for r in ranges), \
                    f"Should not find meaningful dates in: {text}"
    
    # Duration calculation tests
    def test_calculate_duration_with_end_date(self):
        """Test duration calculation with both start and end dates."""
        start = datetime(2020, 1, 1)
        end = datetime(2021, 1, 1)
        duration = self.parser.calculate_duration(start, end)
        
        assert duration == timedelta(days=366)  # 2020 was a leap year
    
    def test_calculate_duration_current_position(self):
        """Test duration calculation for current positions (no end date)."""
        start = datetime(2020, 1, 1)
        duration = self.parser.calculate_duration(start, None)
        
        # Should calculate from start to now
        expected_duration = datetime.now() - start
        # Allow for small differences due to execution time
        assert abs((duration - expected_duration).total_seconds()) < 60
    
    # Position extraction tests
    def test_extract_positions_from_text_simple_case(self):
        """Test position extraction from simple CV text."""
        cv_text = """
        EXPERIÊNCIA PROFISSIONAL
        
        Software Engineer
        TechCorp Inc.
        01/2020 - 12/2021
        Developed web applications using Python and Django.
        
        Junior Developer
        StartupXYZ
        06/2018 - 12/2019
        Worked on mobile applications using React Native.
        """
        
        positions = self.parser.extract_positions_from_text(cv_text)
        
        assert len(positions) >= 2, "Should extract at least 2 positions"
        
        # Check that positions have required fields
        for pos in positions:
            assert pos.title, f"Position should have title: {pos}"
            assert pos.company, f"Position should have company: {pos}"
            assert pos.date_range, f"Position should have date range: {pos}"
    
    def test_extract_positions_from_text_current_position(self):
        """Test position extraction with current position."""
        cv_text = """
        WORK EXPERIENCE
        
        Senior Developer
        TechCorp
        March 2021 - present
        Leading development team and architecting solutions.
        """
        
        positions = self.parser.extract_positions_from_text(cv_text)
        
        assert len(positions) >= 1, "Should extract at least 1 position"
        assert any(pos.date_range.is_current for pos in positions), \
            "Should detect current position"
    
    def test_extract_positions_from_text_mixed_languages(self):
        """Test position extraction from mixed language CV."""
        cv_text = """
        EXPERIÊNCIA / EXPERIENCE
        
        Desenvolvedor Senior / Senior Developer
        Empresa Tech Ltda
        janeiro 2020 - presente
        Desenvolvimento de aplicações web.
        
        Analista de Sistemas
        Consultoria ABC
        March 2018 - December 2019
        System analysis and development.
        """
        
        positions = self.parser.extract_positions_from_text(cv_text)
        
        assert len(positions) >= 2, "Should extract positions from mixed language CV"
    
    def test_extract_positions_from_text_no_experience_section(self):
        """Test position extraction from CV without clear experience section."""
        cv_text = """
        John Doe
        Software Engineer
        
        Skills: Python, Java, JavaScript
        Education: Computer Science Degree
        """
        
        positions = self.parser.extract_positions_from_text(cv_text)
        
        # Should handle gracefully, may or may not find positions
        # depending on the text structure
        assert isinstance(positions, list), "Should return a list"
    
    # Edge cases and error handling
    def test_extract_positions_empty_text(self):
        """Test position extraction from empty text."""
        positions = self.parser.extract_positions_from_text("")
        assert positions == [], "Should return empty list for empty text"
        
        positions = self.parser.extract_positions_from_text("   ")
        assert positions == [], "Should return empty list for whitespace-only text"
    
    def test_extract_positions_malformed_dates(self):
        """Test position extraction with malformed dates."""
        cv_text = """
        EXPERIENCE
        
        Software Engineer
        TechCorp
        invalid-date - another-invalid-date
        Developed applications.
        """
        
        positions = self.parser.extract_positions_from_text(cv_text)
        
        # Should handle gracefully, may extract position with no dates
        assert isinstance(positions, list), "Should return a list even with malformed dates"
    
    def test_convert_to_position_models(self):
        """Test conversion of ExtractedPosition to Position models."""
        # Create sample extracted positions
        date_range = DateRange(
            start_date=datetime(2020, 1, 1),
            end_date=datetime(2021, 12, 31),
            start_confidence=DateConfidence.HIGH,
            end_confidence=DateConfidence.HIGH,
            raw_text="01/2020 - 12/2021",
            is_current=False
        )
        
        extracted_pos = ExtractedPosition(
            title="Software Engineer",
            company="TechCorp",
            date_range=date_range,
            description="Developed web applications",
            section_text="Experience section",
            confidence=0.9
        )
        
        positions = self.parser.convert_to_position_models([extracted_pos])
        
        assert len(positions) == 1, "Should convert one position"
        
        pos = positions[0]
        assert isinstance(pos, Position), "Should return Position model"
        assert pos.title == "Software Engineer"
        assert pos.company == "TechCorp"
        assert pos.start_date == datetime(2020, 1, 1)
        assert pos.end_date == datetime(2021, 12, 31)
        assert pos.is_current is False
        assert pos.duration_months is not None
        assert pos.duration_months > 0
    
    def test_convert_to_position_models_current_position(self):
        """Test conversion of current position to Position model."""
        date_range = DateRange(
            start_date=datetime(2021, 1, 1),
            end_date=None,
            start_confidence=DateConfidence.HIGH,
            end_confidence=DateConfidence.HIGH,
            raw_text="01/2021 - current",
            is_current=True
        )
        
        extracted_pos = ExtractedPosition(
            title="Senior Developer",
            company="NewCorp",
            date_range=date_range,
            description="Leading development",
            section_text="Experience section",
            confidence=0.8
        )
        
        positions = self.parser.convert_to_position_models([extracted_pos])
        
        assert len(positions) == 1
        pos = positions[0]
        assert pos.is_current is True
        assert pos.end_date is None
        assert pos.duration_months is not None  # Should calculate from start to now
    
    # Fuzzy matching tests
    def test_fuzzy_date_matching(self):
        """Test fuzzy date matching capabilities."""
        fuzzy_dates = [
            "around 2020",
            "circa January 2021",
            "approximately March 2019",
            "about 2018",
        ]
        
        for date_str in fuzzy_dates:
            result = self.parser.normalize_date(date_str)
            # Should attempt to extract year at minimum
            if result:
                assert result.year in [2018, 2019, 2020, 2021], \
                    f"Should extract reasonable year from: {date_str}"
    
    def test_date_confidence_levels(self):
        """Test that date confidence levels are assigned correctly."""
        cv_text = "Software Engineer (01/01/2020 - 31/12/2021)"
        ranges = self.parser.extract_dates_from_text(cv_text)
        
        if ranges:
            # Should have high confidence for exact date formats
            assert any(r.start_confidence == DateConfidence.HIGH for r in ranges), \
                "Should have high confidence for exact dates"
    
    def test_overlapping_positions_detection(self):
        """Test detection of overlapping positions in date ranges."""
        cv_text = """
        EXPERIENCE
        
        Consultant
        Company A
        01/2020 - 06/2021
        
        Part-time Developer
        Company B
        03/2020 - 12/2020
        """
        
        positions = self.parser.extract_positions_from_text(cv_text)
        
        # Should extract both positions even if they overlap
        assert len(positions) >= 2, "Should extract overlapping positions"
    
    def test_position_deduplication(self):
        """Test that duplicate positions are removed."""
        cv_text = """
        EXPERIENCE
        
        Software Engineer
        TechCorp
        01/2020 - 12/2021
        Developed applications.
        
        Software Engineer
        TechCorp  
        01/2020 - 12/2021
        Worked on web development.
        """
        
        positions = self.parser.extract_positions_from_text(cv_text)
        
        # Should deduplicate similar positions
        # The exact behavior depends on the similarity threshold
        assert isinstance(positions, list), "Should return deduplicated list"
    
    def test_multiple_date_formats_in_same_text(self):
        """Test handling of multiple date formats in the same CV."""
        cv_text = """
        EXPERIENCE
        
        Senior Developer
        TechCorp
        January 2021 - present
        
        Developer
        StartupXYZ
        03/2019 - 12/2020
        
        Junior Developer
        OldCorp
        2017 - 2018
        """
        
        positions = self.parser.extract_positions_from_text(cv_text)
        
        assert len(positions) >= 3, "Should handle multiple date formats"
        
        # Should detect the current position
        assert any(pos.date_range.is_current for pos in positions), \
            "Should detect current position among multiple formats"
    
    def test_position_extraction_with_special_characters(self):
        """Test position extraction with special characters and formatting."""
        cv_text = """
        ★ EXPERIÊNCIA PROFISSIONAL ★
        
        → Software Engineer @ TechCorp Inc.
        ▪ Period: 01/2020 → 12/2021
        ▪ Responsibilities: Web development
        
        • Junior Developer | StartupXYZ Ltd.
        ◦ Duration: 06/2018 ➤ 12/2019
        ◦ Tasks: Mobile app development
        """
        
        positions = self.parser.extract_positions_from_text(cv_text)
        
        # Should handle special characters and extract positions
        assert len(positions) >= 1, "Should extract positions despite special characters"
    
    def test_year_range_extraction(self):
        """Test extraction of year-only date ranges."""
        cv_text = """
        WORK HISTORY
        
        Software Architect
        BigCorp
        2019 - 2022
        
        Senior Developer
        MidCorp
        2016 - 2018
        """
        
        positions = self.parser.extract_positions_from_text(cv_text)
        
        assert len(positions) >= 2, "Should extract positions with year-only dates"
        
        for pos in positions:
            if pos.date_range.start_date:
                assert pos.date_range.start_date.year in [2016, 2019], \
                    "Should extract correct years"


class TestDateParserEdgeCases:
    """Test suite for edge cases and error conditions."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.parser = DateParser()
    
    def test_leap_year_handling(self):
        """Test handling of leap year dates."""
        leap_year_dates = [
            "29/02/2020",  # Valid leap year date
            "29/02/2021",  # Invalid leap year date
        ]
        
        result_2020 = self.parser.normalize_date(leap_year_dates[0])
        assert result_2020 == datetime(2020, 2, 29), "Should handle valid leap year date"
        
        result_2021 = self.parser.normalize_date(leap_year_dates[1])
        assert result_2021 is None, "Should reject invalid leap year date"
    
    def test_boundary_dates(self):
        """Test handling of boundary dates."""
        boundary_dates = [
            ("01/01/1900", datetime(1900, 1, 1)),
            ("31/12/2099", datetime(2099, 12, 31)),
            ("01/01/2000", datetime(2000, 1, 1)),  # Y2K boundary
        ]
        
        for date_str, expected in boundary_dates:
            result = self.parser.normalize_date(date_str)
            assert result == expected, f"Should handle boundary date: {date_str}"
    
    def test_ambiguous_date_formats(self):
        """Test handling of ambiguous date formats."""
        # These could be interpreted as DD/MM or MM/DD
        ambiguous_dates = [
            "01/02/2020",  # Could be Jan 2 or Feb 1
            "12/01/2020",  # Could be Dec 1 or Jan 12
        ]
        
        for date_str in ambiguous_dates:
            result = self.parser.normalize_date(date_str)
            # Should return a valid date (assuming DD/MM format based on implementation)
            assert result is not None, f"Should handle ambiguous date: {date_str}"
            assert isinstance(result, datetime), f"Should return datetime for: {date_str}"
    
    def test_very_long_text_performance(self):
        """Test performance with very long CV text."""
        # Create a long CV text
        long_cv = "EXPERIENCE\n" + "\n".join([
            f"Position {i}\nCompany {i}\n01/202{i%10} - 12/202{(i+1)%10}\nDescription {i}"
            for i in range(100)
        ])
        
        # Should complete within reasonable time
        import time
        start_time = time.time()
        positions = self.parser.extract_positions_from_text(long_cv)
        end_time = time.time()
        
        assert end_time - start_time < 10, "Should process long text within 10 seconds"
        assert isinstance(positions, list), "Should return list for long text"
    
    def test_unicode_and_special_characters(self):
        """Test handling of Unicode and special characters."""
        unicode_cv = """
        EXPERIÊNCIA PROFISSIONAL
        
        Développeur Senior
        Société Française
        janvier 2020 - présent
        Développement d'applications web.
        
        Desarrollador
        Empresa Española
        marzo 2018 - diciembre 2019
        Desarrollo de software.
        
        Разработчик
        Русская Компания
        01/2016 - 12/2017
        Разработка приложений.
        """
        
        positions = self.parser.extract_positions_from_text(unicode_cv)
        
        # Should handle Unicode characters gracefully
        assert isinstance(positions, list), "Should handle Unicode characters"
    
    def test_malformed_cv_structure(self):
        """Test handling of malformed CV structure."""
        malformed_cvs = [
            "No clear structure just random text with dates 01/2020 and positions",
            "EXPERIENCE\n\n\n\n\nSoftware Engineer\n\n\nTechCorp\n\n\n01/2020",
            "Mixed up order: 01/2020 - 12/2021 TechCorp Software Engineer",
        ]
        
        for cv_text in malformed_cvs:
            positions = self.parser.extract_positions_from_text(cv_text)
            # Should not crash and return a list
            assert isinstance(positions, list), f"Should handle malformed CV: {cv_text[:50]}..."
    
    def test_empty_and_none_inputs(self):
        """Test handling of empty and None inputs."""
        empty_inputs = [None, "", "   ", "\n\n\n", "\t\t\t"]
        
        for empty_input in empty_inputs:
            if empty_input is not None:
                positions = self.parser.extract_positions_from_text(empty_input)
                assert positions == [], f"Should return empty list for: {repr(empty_input)}"
                
                date_result = self.parser.normalize_date(empty_input)
                assert date_result is None, f"Should return None for: {repr(empty_input)}"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])