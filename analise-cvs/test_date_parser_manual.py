#!/usr/bin/env python3
"""
Manual test script for date parsing and position extraction utilities.
This script verifies the core functionality works as expected.
"""

from date_parser import DateParser, DateRange, ExtractedPosition, DateConfidence
from models import Position
from datetime import datetime

def test_date_normalization():
    """Test date normalization functionality."""
    print("=== Testing Date Normalization ===")
    parser = DateParser()
    
    test_cases = [
        ("15/03/2020", datetime(2020, 3, 15)),
        ("03/2020", datetime(2020, 3, 1)),
        ("2020", datetime(2020, 1, 1)),
        ("January 2020", datetime(2020, 1, 1)),
        ("janeiro 2020", datetime(2020, 1, 1)),
        ("current", None),
        ("presente", None),
        ("invalid", None),
    ]
    
    passed = 0
    total = len(test_cases)
    
    for date_str, expected in test_cases:
        result = parser.normalize_date(date_str)
        if result == expected:
            print(f"✓ {date_str:15} -> {result}")
            passed += 1
        else:
            print(f"✗ {date_str:15} -> {result} (expected {expected})")
    
    print(f"Date normalization: {passed}/{total} tests passed\n")
    return passed == total

def test_current_position_detection():
    """Test current position detection."""
    print("=== Testing Current Position Detection ===")
    parser = DateParser()
    
    test_cases = [
        ("Software Engineer (2020 - current)", True),
        ("Developer (2019 - presente)", True),
        ("Manager (2018 - 2020)", False),
        ("Analyst (ongoing)", True),
        ("Lead (still working)", True),
        ("Former position", False),
    ]
    
    passed = 0
    total = len(test_cases)
    
    for text, expected in test_cases:
        result = parser.detect_current_position(text)
        if result == expected:
            print(f"✓ {text:35} -> {result}")
            passed += 1
        else:
            print(f"✗ {text:35} -> {result} (expected {expected})")
    
    print(f"Current position detection: {passed}/{total} tests passed\n")
    return passed == total

def test_date_range_extraction():
    """Test date range extraction from text."""
    print("=== Testing Date Range Extraction ===")
    parser = DateParser()
    
    test_cases = [
        "Software Engineer (01/2020 - 12/2021)",
        "Developer from March 2019 to January 2021",
        "Manager 2018 - 2020",
        "Analyst (15/03/2020 - presente)",
    ]
    
    passed = 0
    total = len(test_cases)
    
    for text in test_cases:
        ranges = parser.extract_dates_from_text(text)
        if ranges and any(r.start_date is not None for r in ranges):
            print(f"✓ {text:45} -> Found {len(ranges)} date range(s)")
            passed += 1
        else:
            print(f"✗ {text:45} -> No meaningful dates found")
    
    print(f"Date range extraction: {passed}/{total} tests passed\n")
    return passed == total

def test_position_extraction():
    """Test position extraction from CV text."""
    print("=== Testing Position Extraction ===")
    parser = DateParser()
    
    cv_text = """
    EXPERIÊNCIA PROFISSIONAL
    
    Software Engineer
    TechCorp Inc.
    01/2020 - 12/2021
    Developed web applications using Python and Django.
    
    Junior Developer
    StartupXYZ
    06/2018 - 12/2019
    Worked on mobile applications using React Native.
    """
    
    positions = parser.extract_positions_from_text(cv_text)
    
    print(f"Extracted {len(positions)} positions from CV text")
    
    for i, pos in enumerate(positions, 1):
        print(f"Position {i}:")
        print(f"  Title: {pos.title}")
        print(f"  Company: {pos.company}")
        print(f"  Start: {pos.date_range.start_date}")
        print(f"  End: {pos.date_range.end_date}")
        print(f"  Current: {pos.date_range.is_current}")
        print(f"  Confidence: {pos.confidence:.2f}")
    
    # Test conversion to Position models
    position_models = parser.convert_to_position_models(positions)
    print(f"\nConverted to {len(position_models)} Position models")
    
    for pos in position_models:
        print(f"Model - Title: {pos.title}, Company: {pos.company}, Duration: {pos.duration_months} months")
    
    print(f"Position extraction: {'✓' if len(positions) > 0 else '✗'} Found positions\n")
    return len(positions) > 0

def test_edge_cases():
    """Test edge cases and error handling."""
    print("=== Testing Edge Cases ===")
    parser = DateParser()
    
    edge_cases = [
        "",  # Empty string
        "   ",  # Whitespace only
        "No dates here",  # No dates
        "29/02/2020",  # Leap year
        "32/13/2020",  # Invalid date
        "Software Engineer at TechCorp",  # No dates
    ]
    
    passed = 0
    total = len(edge_cases)
    
    for case in edge_cases:
        try:
            result = parser.normalize_date(case)
            positions = parser.extract_positions_from_text(case)
            print(f"✓ {case:25} -> Handled gracefully")
            passed += 1
        except Exception as e:
            print(f"✗ {case:25} -> Error: {e}")
    
    print(f"Edge cases: {passed}/{total} tests passed\n")
    return passed == total

def main():
    """Run all tests."""
    print("Date Parser and Position Extraction Utilities Test Suite")
    print("=" * 60)
    
    tests = [
        test_date_normalization,
        test_current_position_detection,
        test_date_range_extraction,
        test_position_extraction,
        test_edge_cases,
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_func in tests:
        if test_func():
            passed_tests += 1
    
    print("=" * 60)
    print(f"Overall Result: {passed_tests}/{total_tests} test suites passed")
    
    if passed_tests == total_tests:
        print("✓ All tests passed! Date parsing and position extraction utilities are working correctly.")
        return True
    else:
        print("✗ Some tests failed. Please review the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)