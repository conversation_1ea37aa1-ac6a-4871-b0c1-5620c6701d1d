"""
Unit tests for enhanced data models.

This module contains comprehensive tests for all Pydantic models,
including validation, serialization, and business logic.
"""

import pytest
from datetime import datetime, timedelta
from typing import Dict, Any
import json

from models import (
    # Enums
    RedFlagType, Severity, GapSeverity, ProcessingStatus, SkillContext,
    
    # Core models
    Position, EmploymentGap, SkillClassification, SkillAnalysis,
    JobStabilityAnalysis, RedFlag, ExperienceTimeline, AnalysisConfig,
    CandidateAssessment, EnhancedAssessment, BatchProcessingResult, ExportConfig
)


class TestEnums:
    """Test all enum classes."""
    
    def test_red_flag_type_enum(self):
        """Test RedFlagType enum values."""
        assert RedFlagType.EMPLOYMENT_GAP == "employment_gap"
        assert RedFlagType.JOB_HOPPING == "job_hopping"
        assert RedFlagType.SHORT_POSITION == "short_position"
        assert RedFlagType.SKILL_MISMATCH == "skill_mismatch"
        assert RedFlagType.ACADEMIC_ONLY_SKILL == "academic_only_skill"
        assert RedFlagType.OUTDATED_SKILL == "outdated_skill"
        assert RedFlagType.MISSING_REQUIRED_SKILL == "missing_required_skill"
        assert RedFlagType.INCONSISTENT_DATES == "inconsistent_dates"
    
    def test_severity_enum(self):
        """Test Severity enum values."""
        assert Severity.LOW == "low"
        assert Severity.MEDIUM == "medium"
        assert Severity.HIGH == "high"
        assert Severity.CRITICAL == "critical"
    
    def test_gap_severity_enum(self):
        """Test GapSeverity enum values."""
        assert GapSeverity.MINOR == "minor"
        assert GapSeverity.MODERATE == "moderate"
        assert GapSeverity.SIGNIFICANT == "significant"
        assert GapSeverity.MAJOR == "major"
    
    def test_processing_status_enum(self):
        """Test ProcessingStatus enum values."""
        assert ProcessingStatus.PENDING == "pending"
        assert ProcessingStatus.PROCESSING == "processing"
        assert ProcessingStatus.COMPLETED == "completed"
        assert ProcessingStatus.FAILED == "failed"
        assert ProcessingStatus.PARTIAL_SUCCESS == "partial_success"
    
    def test_skill_context_enum(self):
        """Test SkillContext enum values."""
        assert SkillContext.PROFESSIONAL == "professional"
        assert SkillContext.ACADEMIC == "academic"
        assert SkillContext.CERTIFICATION == "certification"
        assert SkillContext.PERSONAL_PROJECT == "personal_project"
        assert SkillContext.UNCLEAR == "unclear"


class TestPosition:
    """Test Position model."""
    
    def test_position_creation(self):
        """Test basic position creation."""
        position = Position(
            title="Software Engineer",
            company="Tech Corp",
            description="Developed software applications"
        )
        
        assert position.title == "Software Engineer"
        assert position.company == "Tech Corp"
        assert position.description == "Developed software applications"
        assert position.is_current is False
        assert position.skills_used == []
    
    def test_position_with_dates(self):
        """Test position with start and end dates."""
        start_date = datetime(2020, 1, 1)
        end_date = datetime(2022, 6, 30)
        
        position = Position(
            title="Developer",
            company="StartupCo",
            start_date=start_date,
            end_date=end_date,
            skills_used=["Python", "Django", "PostgreSQL"]
        )
        
        assert position.start_date == start_date
        assert position.end_date == end_date
        assert len(position.skills_used) == 3
    
    def test_calculate_duration(self):
        """Test duration calculation."""
        start_date = datetime(2020, 1, 1)
        end_date = datetime(2020, 7, 1)  # 6 months
        
        position = Position(
            title="Intern",
            company="Company",
            start_date=start_date,
            end_date=end_date
        )
        
        duration = position.calculate_duration()
        assert duration is not None
        # Duration calculation uses 30.44 days per month average
        # Jan 1 to July 1 is 181 days, which equals ~5.9 months, rounded to 5
        assert duration == 5
    
    def test_calculate_duration_no_start_date(self):
        """Test duration calculation without start date."""
        position = Position(
            title="Developer",
            company="Company"
        )
        
        duration = position.calculate_duration()
        assert duration is None
    
    def test_calculate_duration_current_position(self):
        """Test duration calculation for current position."""
        start_date = datetime(2023, 1, 1)
        
        position = Position(
            title="Senior Developer",
            company="Current Corp",
            start_date=start_date,
            is_current=True
        )
        
        duration = position.calculate_duration()
        assert duration is not None
        assert duration > 0


class TestEmploymentGap:
    """Test EmploymentGap model."""
    
    def test_employment_gap_creation(self):
        """Test basic employment gap creation."""
        start_date = datetime(2020, 6, 1)
        end_date = datetime(2020, 12, 1)
        
        gap = EmploymentGap(
            start_date=start_date,
            end_date=end_date,
            duration_months=6,
            severity=GapSeverity.MODERATE,
            description="Career break"
        )
        
        assert gap.start_date == start_date
        assert gap.end_date == end_date
        assert gap.duration_months == 6
        assert gap.severity == GapSeverity.MODERATE
        assert gap.description == "Career break"
    
    def test_from_positions_with_gap(self):
        """Test creating gap from two positions."""
        prev_position = Position(
            title="Developer",
            company="OldCorp",
            start_date=datetime(2019, 1, 1),
            end_date=datetime(2020, 3, 31)
        )
        
        next_position = Position(
            title="Senior Developer",
            company="NewCorp",
            start_date=datetime(2020, 10, 1),
            end_date=datetime(2022, 12, 31)
        )
        
        gap = EmploymentGap.from_positions(prev_position, next_position)
        
        assert gap is not None
        assert gap.start_date == datetime(2020, 3, 31)
        assert gap.end_date == datetime(2020, 10, 1)
        assert gap.duration_months == 6
        assert gap.severity == GapSeverity.MODERATE
    
    def test_from_positions_no_gap(self):
        """Test no gap between consecutive positions."""
        prev_position = Position(
            title="Developer",
            company="OldCorp",
            start_date=datetime(2019, 1, 1),
            end_date=datetime(2020, 3, 31)
        )
        
        next_position = Position(
            title="Senior Developer",
            company="NewCorp",
            start_date=datetime(2020, 4, 1),  # No gap
            end_date=datetime(2022, 12, 31)
        )
        
        gap = EmploymentGap.from_positions(prev_position, next_position)
        assert gap is None
    
    def test_from_positions_overlapping(self):
        """Test overlapping positions (no gap)."""
        prev_position = Position(
            title="Developer",
            company="OldCorp",
            start_date=datetime(2019, 1, 1),
            end_date=datetime(2020, 6, 30)
        )
        
        next_position = Position(
            title="Senior Developer",
            company="NewCorp",
            start_date=datetime(2020, 4, 1),  # Overlaps
            end_date=datetime(2022, 12, 31)
        )
        
        gap = EmploymentGap.from_positions(prev_position, next_position)
        assert gap is None
    
    def test_gap_severity_classification(self):
        """Test gap severity classification based on duration."""
        # Test different gap durations and their severity
        test_cases = [
            (4, GapSeverity.MINOR),      # 4 months
            (8, GapSeverity.MODERATE),   # 8 months
            (18, GapSeverity.SIGNIFICANT), # 18 months
            (30, GapSeverity.MAJOR)      # 30 months
        ]
        
        for months, expected_severity in test_cases:
            prev_pos = Position(
                title="Dev", company="A",
                start_date=datetime(2020, 1, 1),
                end_date=datetime(2020, 3, 31)
            )
            next_pos = Position(
                title="Dev", company="B",
                start_date=datetime(2020, 3, 31) + timedelta(days=months * 30),
                end_date=datetime(2022, 1, 1)
            )
            
            gap = EmploymentGap.from_positions(prev_pos, next_pos)
            if gap:  # Only test if gap was created
                assert gap.severity == expected_severity


class TestSkillClassification:
    """Test SkillClassification model."""
    
    def test_skill_classification_creation(self):
        """Test basic skill classification creation."""
        skill_class = SkillClassification(
            skill="Python",
            context=SkillContext.PROFESSIONAL,
            is_recent=True,
            positions_used=["Software Engineer", "Senior Developer"],
            confidence=0.9
        )
        
        assert skill_class.skill == "Python"
        assert skill_class.context == SkillContext.PROFESSIONAL
        assert skill_class.is_recent is True
        assert len(skill_class.positions_used) == 2
        assert skill_class.confidence == 0.9
    
    def test_skill_classification_defaults(self):
        """Test skill classification with default values."""
        skill_class = SkillClassification(
            skill="JavaScript",
            context=SkillContext.ACADEMIC
        )
        
        assert skill_class.is_recent is False
        assert skill_class.positions_used == []
        assert skill_class.confidence == 0.5


class TestRedFlag:
    """Test RedFlag model."""
    
    def test_red_flag_creation(self):
        """Test basic red flag creation."""
        red_flag = RedFlag(
            type=RedFlagType.EMPLOYMENT_GAP,
            severity=Severity.HIGH,
            description="6-month employment gap detected",
            details={"gap_duration": 6, "gap_start": "2020-06-01"},
            position_related="Software Engineer at TechCorp"
        )
        
        assert red_flag.type == RedFlagType.EMPLOYMENT_GAP
        assert red_flag.severity == Severity.HIGH
        assert red_flag.description == "6-month employment gap detected"
        assert red_flag.details["gap_duration"] == 6
        assert red_flag.position_related == "Software Engineer at TechCorp"
    
    def test_red_flag_str_representation(self):
        """Test string representation of red flag."""
        red_flag = RedFlag(
            type=RedFlagType.JOB_HOPPING,
            severity=Severity.MEDIUM,
            description="Multiple short positions detected"
        )
        
        str_repr = str(red_flag)
        assert "MEDIUM:" in str_repr
        assert "Multiple short positions detected" in str_repr


class TestAnalysisConfig:
    """Test AnalysisConfig model."""
    
    def test_analysis_config_defaults(self):
        """Test analysis config with default values."""
        config = AnalysisConfig()
        
        assert config.gap_threshold_months == 3
        assert config.short_position_threshold_months == 6
        assert config.job_hopping_threshold == 3
        assert config.recent_experience_years == 5
        assert config.llm_temperature == 0.7
        assert config.llm_max_tokens == 10000
        assert config.max_retries == 3
        assert config.timeout_seconds == 300
        
        # Test default skill weights
        assert config.skill_weights["professional"] == 1.0
        assert config.skill_weights["academic"] == 0.3
        assert config.skill_weights["certification"] == 0.8
        assert config.skill_weights["personal_project"] == 0.5
    
    def test_analysis_config_custom_values(self):
        """Test analysis config with custom values."""
        config = AnalysisConfig(
            gap_threshold_months=6,
            short_position_threshold_months=12,
            mandatory_skills=["Python", "SQL"],
            preferred_skills=["Docker", "AWS"],
            llm_temperature=0.5
        )
        
        assert config.gap_threshold_months == 6
        assert config.short_position_threshold_months == 12
        assert len(config.mandatory_skills) == 2
        assert len(config.preferred_skills) == 2
        assert config.llm_temperature == 0.5
    
    def test_analysis_config_validation(self):
        """Test analysis config field validation."""
        # Test invalid values that should raise validation errors
        with pytest.raises(ValueError):
            AnalysisConfig(gap_threshold_months=0)  # Below minimum
        
        with pytest.raises(ValueError):
            AnalysisConfig(llm_temperature=3.0)  # Above maximum
        
        with pytest.raises(ValueError):
            AnalysisConfig(max_retries=0)  # Below minimum


class TestCandidateAssessment:
    """Test original CandidateAssessment model."""
    
    def test_candidate_assessment_creation(self):
        """Test basic candidate assessment creation."""
        assessment = CandidateAssessment(
            candidate_name="John Doe",
            email="<EMAIL>",
            match_score=0.85,
            matched_sections=["Experience", "Skills"],
            experience_years=5,
            certifications=["AWS Certified", "Python Certified"],
            summary="Experienced software developer"
        )
        
        assert assessment.candidate_name == "John Doe"
        assert assessment.email == "<EMAIL>"
        assert assessment.match_score == 0.85
        assert len(assessment.matched_sections) == 2
        assert assessment.experience_years == 5
        assert len(assessment.certifications) == 2
        assert assessment.summary == "Experienced software developer"
    
    def test_candidate_assessment_defaults(self):
        """Test candidate assessment with default values."""
        assessment = CandidateAssessment(match_score=0.5)
        
        assert assessment.candidate_name is None
        assert assessment.contact_phone is None
        assert assessment.address is None
        assert assessment.email is None
        assert assessment.github is None
        assert assessment.linkedin is None
        assert assessment.age is None
        assert assessment.current_position is None
        assert assessment.matched_sections == []
        assert assessment.experience_years == 0
        assert assessment.certifications == []
        assert assessment.summary == ""
    
    def test_match_score_validation(self):
        """Test match score validation."""
        # Valid scores
        CandidateAssessment(match_score=0.0)
        CandidateAssessment(match_score=0.5)
        CandidateAssessment(match_score=1.0)
        
        # Invalid scores should raise validation errors
        with pytest.raises(ValueError):
            CandidateAssessment(match_score=-0.1)
        
        with pytest.raises(ValueError):
            CandidateAssessment(match_score=1.1)


class TestEnhancedAssessment:
    """Test EnhancedAssessment model."""
    
    def test_enhanced_assessment_creation(self):
        """Test enhanced assessment creation."""
        assessment = EnhancedAssessment(
            candidate_name="Jane Smith",
            match_score=0.9,
            processing_status=ProcessingStatus.COMPLETED,
            analysis_timestamp=datetime.now()
        )
        
        assert assessment.candidate_name == "Jane Smith"
        assert assessment.match_score == 0.9
        assert assessment.processing_status == ProcessingStatus.COMPLETED
        assert assessment.analysis_timestamp is not None
        
        # Test default values for enhanced fields
        assert assessment.positions == []
        assert assessment.employment_gaps == []
        assert assessment.red_flags == []
        assert assessment.error_messages == []
        assert assessment.detailed_match_scores == {}
        assert assessment.confidence_score == 0.5
    
    def test_add_red_flag(self):
        """Test adding red flags to assessment."""
        assessment = EnhancedAssessment(match_score=0.7)
        
        assessment.add_red_flag(
            flag_type=RedFlagType.EMPLOYMENT_GAP,
            severity=Severity.MEDIUM,
            description="3-month gap detected",
            details={"duration": 3},
            position_related="Developer at StartupCo"
        )
        
        assert len(assessment.red_flags) == 1
        red_flag = assessment.red_flags[0]
        assert red_flag.type == RedFlagType.EMPLOYMENT_GAP
        assert red_flag.severity == Severity.MEDIUM
        assert red_flag.description == "3-month gap detected"
        assert red_flag.details["duration"] == 3
        assert red_flag.position_related == "Developer at StartupCo"
    
    def test_get_red_flags_by_severity(self):
        """Test filtering red flags by severity."""
        assessment = EnhancedAssessment(match_score=0.6)
        
        # Add red flags with different severities
        assessment.add_red_flag(RedFlagType.EMPLOYMENT_GAP, Severity.LOW, "Minor gap")
        assessment.add_red_flag(RedFlagType.JOB_HOPPING, Severity.HIGH, "Job hopping")
        assessment.add_red_flag(RedFlagType.SHORT_POSITION, Severity.HIGH, "Short position")
        assessment.add_red_flag(RedFlagType.SKILL_MISMATCH, Severity.CRITICAL, "Missing skill")
        
        low_flags = assessment.get_red_flags_by_severity(Severity.LOW)
        high_flags = assessment.get_red_flags_by_severity(Severity.HIGH)
        critical_flags = assessment.get_critical_red_flags()
        
        assert len(low_flags) == 1
        assert len(high_flags) == 2
        assert len(critical_flags) == 1
        assert critical_flags[0].type == RedFlagType.SKILL_MISMATCH
    
    def test_calculate_overall_risk_score(self):
        """Test overall risk score calculation."""
        assessment = EnhancedAssessment(match_score=0.8)
        
        # No red flags should result in 0 risk
        assert assessment.calculate_overall_risk_score() == 0.0
        
        # Add red flags and test risk calculation
        assessment.add_red_flag(RedFlagType.EMPLOYMENT_GAP, Severity.LOW, "Minor issue")
        assessment.add_red_flag(RedFlagType.JOB_HOPPING, Severity.HIGH, "Major issue")
        
        risk_score = assessment.calculate_overall_risk_score()
        assert 0.0 < risk_score <= 1.0
    
    def test_from_basic_assessment(self):
        """Test creating enhanced assessment from basic assessment."""
        basic = CandidateAssessment(
            candidate_name="Test Candidate",
            email="<EMAIL>",
            match_score=0.75,
            experience_years=3,
            summary="Test summary"
        )
        
        enhanced = EnhancedAssessment.from_basic_assessment(basic)
        
        assert enhanced.candidate_name == "Test Candidate"
        assert enhanced.email == "<EMAIL>"
        assert enhanced.match_score == 0.75
        assert enhanced.experience_years == 3
        assert enhanced.summary == "Test summary"
        
        # Enhanced fields should have default values
        assert enhanced.positions == []
        assert enhanced.red_flags == []
        assert enhanced.processing_status == ProcessingStatus.PENDING


class TestBatchProcessingResult:
    """Test BatchProcessingResult model."""
    
    def test_batch_processing_result_creation(self):
        """Test batch processing result creation."""
        result = BatchProcessingResult()
        
        assert result.assessments == []
        assert result.total_processed == 0
        assert result.successful_count == 0
        assert result.failed_count == 0
        assert result.processing_time_seconds == 0.0
        assert result.errors == []
    
    def test_add_assessment_successful(self):
        """Test adding successful assessment."""
        result = BatchProcessingResult()
        assessment = EnhancedAssessment(
            match_score=0.8,
            processing_status=ProcessingStatus.COMPLETED
        )
        
        result.add_assessment(assessment)
        
        assert len(result.assessments) == 1
        assert result.total_processed == 1
        assert result.successful_count == 1
        assert result.failed_count == 0
    
    def test_add_assessment_failed(self):
        """Test adding failed assessment."""
        result = BatchProcessingResult()
        assessment = EnhancedAssessment(
            match_score=0.0,
            processing_status=ProcessingStatus.FAILED,
            error_messages=["PDF extraction failed", "LLM API error"]
        )
        
        result.add_assessment(assessment)
        
        assert len(result.assessments) == 1
        assert result.total_processed == 1
        assert result.successful_count == 0
        assert result.failed_count == 1
        assert len(result.errors) == 2


class TestExportConfig:
    """Test ExportConfig model."""
    
    def test_export_config_defaults(self):
        """Test export config with default values."""
        config = ExportConfig()
        
        assert config.include_red_flags is True
        assert config.include_skill_analysis is True
        assert config.include_position_details is True
        assert config.include_processing_metadata is False
        assert config.format == "excel"
        assert config.filename_prefix == "cv_analysis"
    
    def test_export_config_custom_values(self):
        """Test export config with custom values."""
        config = ExportConfig(
            include_red_flags=False,
            format="csv",
            filename_prefix="recruitment_analysis"
        )
        
        assert config.include_red_flags is False
        assert config.format == "csv"
        assert config.filename_prefix == "recruitment_analysis"
    
    def test_export_config_format_validation(self):
        """Test export format validation."""
        # Valid formats
        ExportConfig(format="excel")
        ExportConfig(format="csv")
        ExportConfig(format="json")
        
        # Invalid format should raise validation error
        with pytest.raises(ValueError):
            ExportConfig(format="pdf")


class TestModelSerialization:
    """Test model serialization and deserialization."""
    
    def test_enhanced_assessment_serialization(self):
        """Test enhanced assessment JSON serialization."""
        assessment = EnhancedAssessment(
            candidate_name="Test User",
            match_score=0.85,
            processing_status=ProcessingStatus.COMPLETED,
            analysis_timestamp=datetime(2024, 1, 15, 10, 30, 0)
        )
        
        # Add some complex data
        assessment.add_red_flag(
            RedFlagType.EMPLOYMENT_GAP,
            Severity.MEDIUM,
            "Gap detected",
            {"duration": 4}
        )
        
        # Test serialization
        json_data = assessment.model_dump()
        assert json_data["candidate_name"] == "Test User"
        assert json_data["match_score"] == 0.85
        assert json_data["processing_status"] == "completed"
        assert len(json_data["red_flags"]) == 1
        
        # Test JSON string serialization
        json_str = assessment.model_dump_json()
        assert isinstance(json_str, str)
        
        # Test deserialization
        parsed_data = json.loads(json_str)
        reconstructed = EnhancedAssessment(**parsed_data)
        assert reconstructed.candidate_name == assessment.candidate_name
        assert reconstructed.match_score == assessment.match_score
        assert len(reconstructed.red_flags) == 1
    
    def test_analysis_config_serialization(self):
        """Test analysis config serialization."""
        config = AnalysisConfig(
            gap_threshold_months=4,
            mandatory_skills=["Python", "SQL"],
            preferred_skills=["Docker"]
        )
        
        json_data = config.model_dump()
        assert json_data["gap_threshold_months"] == 4
        assert len(json_data["mandatory_skills"]) == 2
        assert len(json_data["preferred_skills"]) == 1
        
        # Test reconstruction
        reconstructed = AnalysisConfig(**json_data)
        assert reconstructed.gap_threshold_months == 4
        assert reconstructed.mandatory_skills == ["Python", "SQL"]
        assert reconstructed.preferred_skills == ["Docker"]


if __name__ == "__main__":
    # Run tests if script is executed directly
    pytest.main([__file__, "-v"])