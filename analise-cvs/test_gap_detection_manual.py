#!/usr/bin/env python3
"""
Manual test to verify employment gap detection functionality works correctly.
"""

from datetime import datetime
from career_analyzer import CareerAnalyzer
from models import Position, AnalysisConfig

def test_employment_gap_detection():
    """Test the employment gap detection with various scenarios."""
    
    # Create analyzer with default config
    config = AnalysisConfig()
    analyzer = CareerAnalyzer(config)
    
    print("Testing Employment Gap Detection")
    print("=" * 50)
    
    # Test Case 1: Normal gap detection
    print("\n1. Testing normal gap detection:")
    positions = [
        Position(
            title="Software Engineer",
            company="Company A",
            start_date=datetime(2020, 1, 1),
            end_date=datetime(2021, 6, 30),
            description="First job"
        ),
        Position(
            title="Senior Software Engineer", 
            company="Company B",
            start_date=datetime(2022, 1, 1),  # 6-month gap
            end_date=datetime(2023, 12, 31),
            description="Second job"
        ),
        Position(
            title="Tech Lead",
            company="Company C", 
            start_date=datetime(2024, 2, 1),  # 1-month gap (below threshold)
            end_date=None,
            is_current=True,
            description="Current job"
        )
    ]
    
    gaps = analyzer.detect_employment_gaps(positions)
    print(f"Found {len(gaps)} gaps:")
    for gap in gaps:
        print(f"  - {gap.description}")
        print(f"    Duration: {gap.duration_months} months, Severity: {gap.severity}")
    
    # Test Case 2: Edge cases
    print("\n2. Testing edge cases:")
    
    # Overlapping positions
    overlapping_positions = [
        Position(
            title="Job 1",
            company="Company A",
            start_date=datetime(2023, 1, 1),
            end_date=datetime(2023, 8, 31)
        ),
        Position(
            title="Job 2", 
            company="Company B",
            start_date=datetime(2023, 6, 1),  # Overlaps with Job 1
            end_date=datetime(2023, 12, 31)
        )
    ]
    
    overlapping_gaps = analyzer.detect_employment_gaps(overlapping_positions)
    print(f"Overlapping positions - Found {len(overlapping_gaps)} gaps (should be 0)")
    
    # Missing dates
    missing_dates_positions = [
        Position(
            title="Job 1",
            company="Company A", 
            start_date=datetime(2023, 1, 1),
            end_date=None  # Missing end date
        ),
        Position(
            title="Job 2",
            company="Company B",
            start_date=datetime(2023, 6, 1),
            end_date=datetime(2023, 12, 31)
        )
    ]
    
    missing_gaps = analyzer.detect_employment_gaps(missing_dates_positions)
    print(f"Missing dates - Found {len(missing_gaps)} gaps (should be 0)")
    
    # Test Case 3: Different gap severities
    print("\n3. Testing gap severity classification:")
    test_durations = [4, 8, 18, 30]
    for months in test_durations:
        severity = analyzer._calculate_gap_severity(months)
        print(f"  {months} months -> {severity}")
    
    # Test Case 4: Red flag generation
    print("\n4. Testing red flag generation:")
    from models import JobStabilityAnalysis
    
    red_flags = analyzer.generate_career_red_flags(positions, gaps, JobStabilityAnalysis())
    print(f"Generated {len(red_flags)} red flags:")
    for flag in red_flags:
        print(f"  - {flag.type}: {flag.description} (Severity: {flag.severity})")
    
    print("\n" + "=" * 50)
    print("Employment gap detection test completed successfully!")

if __name__ == "__main__":
    test_employment_gap_detection()