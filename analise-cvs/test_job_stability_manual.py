#!/usr/bin/env python3
"""
Manual test to verify job stability analysis implementation.
This test demonstrates the complete job stability analysis workflow.
"""

from datetime import datetime
from career_analyzer import CareerAnalyzer
from models import Position, AnalysisConfig, RedFlagType, Severity

def test_job_stability_complete_workflow():
    """Test the complete job stability analysis workflow."""
    
    # Create configuration with custom thresholds
    config = AnalysisConfig(
        short_position_threshold_months=6,
        job_hopping_threshold=3,
        gap_threshold_months=3
    )
    
    analyzer = CareerAnalyzer(config)
    
    # Create test positions with job hopping pattern
    positions = [
        Position(
            title="Junior Developer",
            company="StartupA",
            start_date=datetime(2022, 1, 1),
            end_date=datetime(2022, 4, 30),  # 4 months - short position
            description="First job out of college"
        ),
        Position(
            title="Developer",
            company="StartupB", 
            start_date=datetime(2022, 6, 1),  # 1 month gap
            end_date=datetime(2022, 9, 30),   # 4 months - short position
            description="Second job"
        ),
        Position(
            title="Software Engineer",
            company="TechCorpC",
            start_date=datetime(2022, 11, 1), # 1 month gap
            end_date=datetime(2023, 2, 28),   # 4 months - short position
            description="Third job"
        ),
        Position(
            title="Senior Developer",
            company="BigTechD",
            start_date=datetime(2023, 4, 1),  # 1 month gap
            end_date=datetime(2024, 12, 31),  # 21 months - stable position
            description="Current stable position"
        )
    ]
    
    print("=== Job Stability Analysis Test ===")
    print(f"Testing with {len(positions)} positions")
    print(f"Short position threshold: {config.short_position_threshold_months} months")
    print(f"Job hopping threshold: {config.job_hopping_threshold} short positions")
    
    # Analyze job stability
    stability_analysis = analyzer.analyze_job_stability(positions)
    
    print(f"\n=== Stability Analysis Results ===")
    print(f"Total positions: {stability_analysis.total_positions}")
    print(f"Short positions count: {stability_analysis.short_positions_count}")
    print(f"Average position duration: {stability_analysis.average_position_duration_months:.1f} months")
    print(f"Job hopping score: {stability_analysis.job_hopping_score:.2f}")
    print(f"Stability score: {stability_analysis.stability_score:.2f}")
    print(f"Career progression trend: {stability_analysis.career_progression_trend}")
    
    # Detect employment gaps
    gaps = analyzer.detect_employment_gaps(positions)
    print(f"\n=== Employment Gaps ===")
    print(f"Number of gaps detected: {len(gaps)}")
    for gap in gaps:
        print(f"- {gap.duration_months} month gap ({gap.severity.value}): {gap.description}")
    
    # Generate red flags
    red_flags = analyzer.generate_career_red_flags(positions, gaps, stability_analysis)
    
    print(f"\n=== Red Flags Generated ===")
    print(f"Total red flags: {len(red_flags)}")
    
    # Group red flags by type
    flag_types = {}
    for flag in red_flags:
        if flag.type not in flag_types:
            flag_types[flag.type] = []
        flag_types[flag.type].append(flag)
    
    for flag_type, flags in flag_types.items():
        print(f"\n{flag_type.value.upper()} ({len(flags)} flags):")
        for flag in flags:
            print(f"  - {flag.severity.value.upper()}: {flag.description}")
            if flag.details:
                for key, value in flag.details.items():
                    print(f"    {key}: {value}")
    
    # Verify requirements compliance
    print(f"\n=== Requirements Verification ===")
    
    # Requirement 2.3: Flag positions lasting less than 6 months
    short_position_flags = [f for f in red_flags if f.type == RedFlagType.SHORT_POSITION]
    expected_short_positions = 3  # First 3 positions are < 6 months
    print(f"✓ Short position flags: {len(short_position_flags)} (expected: {expected_short_positions})")
    assert len(short_position_flags) == expected_short_positions, f"Expected {expected_short_positions} short position flags, got {len(short_position_flags)}"
    
    # Requirement 2.4: Raise job hopping alert when multiple short positions found
    job_hopping_flags = [f for f in red_flags if f.type == RedFlagType.JOB_HOPPING]
    print(f"✓ Job hopping flags: {len(job_hopping_flags)} (expected: 1)")
    assert len(job_hopping_flags) == 1, f"Expected 1 job hopping flag, got {len(job_hopping_flags)}"
    
    # Verify job hopping flag details
    job_hopping_flag = job_hopping_flags[0]
    assert job_hopping_flag.severity in [Severity.MEDIUM, Severity.HIGH], f"Job hopping flag should be MEDIUM or HIGH severity, got {job_hopping_flag.severity}"
    assert job_hopping_flag.details["short_positions_count"] == 3, f"Expected 3 short positions in details, got {job_hopping_flag.details['short_positions_count']}"
    
    print(f"✓ Job hopping flag severity: {job_hopping_flag.severity.value}")
    print(f"✓ Job hopping flag details: {job_hopping_flag.details}")
    
    # Test configurable thresholds
    print(f"\n=== Testing Configurable Thresholds ===")
    
    # Test with different short position threshold
    config_strict = AnalysisConfig(
        short_position_threshold_months=12,  # Stricter threshold
        job_hopping_threshold=2
    )
    analyzer_strict = CareerAnalyzer(config_strict)
    stability_strict = analyzer_strict.analyze_job_stability(positions)
    
    print(f"With 12-month threshold:")
    print(f"  Short positions count: {stability_strict.short_positions_count}")
    print(f"  Stability score: {stability_strict.stability_score:.2f}")
    
    # Test with lenient threshold
    config_lenient = AnalysisConfig(
        short_position_threshold_months=3,  # More lenient
        job_hopping_threshold=5
    )
    analyzer_lenient = CareerAnalyzer(config_lenient)
    stability_lenient = analyzer_lenient.analyze_job_stability(positions)
    
    print(f"With 3-month threshold:")
    print(f"  Short positions count: {stability_lenient.short_positions_count}")
    print(f"  Stability score: {stability_lenient.stability_score:.2f}")
    
    print(f"\n✅ All requirements verified successfully!")
    print(f"✅ Job stability analysis implementation is complete and working correctly!")

if __name__ == "__main__":
    test_job_stability_complete_workflow()