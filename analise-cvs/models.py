"""
Enhanced data models for CV Analyzer Enhancement.

This module contains all the Pydantic models used throughout the application,
including the enhanced assessment model and supporting enums and classes.
"""

from datetime import datetime, timedelta
from enum import Enum
from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field


# Enums for categorization and status tracking
class RedFlagType(str, Enum):
    """Types of red flags that can be detected in candidate analysis."""
    EMPLOYMENT_GAP = "employment_gap"
    JOB_HOPPING = "job_hopping"
    SHORT_POSITION = "short_position"
    SKILL_MISMATCH = "skill_mismatch"
    ACADEMIC_ONLY_SKILL = "academic_only_skill"
    OUTDATED_SKILL = "outdated_skill"
    MISSING_REQUIRED_SKILL = "missing_required_skill"
    INCONSISTENT_DATES = "inconsistent_dates"


class Severity(str, Enum):
    """Severity levels for red flags and issues."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class GapSeverity(str, Enum):
    """Specific severity levels for employment gaps."""
    MINOR = "minor"  # 3-6 months
    MODERATE = "moderate"  # 6-12 months
    SIGNIFICANT = "significant"  # 12-24 months
    MAJOR = "major"  # 24+ months


class ProcessingStatus(str, Enum):
    """Status of CV processing."""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    PARTIAL_SUCCESS = "partial_success"


class SkillContext(str, Enum):
    """Context in which a skill was acquired or used."""
    PROFESSIONAL = "professional"
    ACADEMIC = "academic"
    CERTIFICATION = "certification"
    PERSONAL_PROJECT = "personal_project"
    UNCLEAR = "unclear"


# Supporting data models
class Position(BaseModel):
    """Represents a work position/job in a candidate's career history."""
    title: str
    company: str
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    duration_months: Optional[int] = None
    is_current: bool = False
    description: str = ""
    skills_used: List[str] = Field(default_factory=list)
    
    def calculate_duration(self) -> Optional[int]:
        """Calculate duration in months if dates are available."""
        if not self.start_date:
            return None
        
        end = self.end_date or datetime.now()
        delta = end - self.start_date
        return max(1, int(delta.days / 30.44))  # Average days per month


class EmploymentGap(BaseModel):
    """Represents a gap in employment history."""
    start_date: datetime
    end_date: datetime
    duration_months: int
    severity: GapSeverity
    description: str = ""
    
    @classmethod
    def from_positions(cls, prev_position: Position, next_position: Position) -> Optional["EmploymentGap"]:
        """Create an employment gap from two consecutive positions."""
        if not prev_position.end_date or not next_position.start_date:
            return None
        
        if prev_position.end_date >= next_position.start_date:
            return None  # No gap or overlap
        
        delta = next_position.start_date - prev_position.end_date
        duration_months = int(delta.days / 30.44)
        
        if duration_months < 3:  # Less than 3 months is not considered a significant gap
            return None
        
        # Determine severity based on duration
        if duration_months < 6:
            severity = GapSeverity.MINOR
        elif duration_months < 12:
            severity = GapSeverity.MODERATE
        elif duration_months < 24:
            severity = GapSeverity.SIGNIFICANT
        else:
            severity = GapSeverity.MAJOR
        
        return cls(
            start_date=prev_position.end_date,
            end_date=next_position.start_date,
            duration_months=duration_months,
            severity=severity,
            description=f"Gap between {prev_position.company} and {next_position.company}"
        )


class SkillClassification(BaseModel):
    """Classification of a skill with context and recency information."""
    skill: str
    context: SkillContext
    is_recent: bool = False  # Used in last 3 positions or 5 years
    positions_used: List[str] = Field(default_factory=list)  # Position titles where used
    confidence: float = Field(ge=0.0, le=1.0, default=0.5)


class SkillAnalysis(BaseModel):
    """Comprehensive analysis of candidate's skills."""
    professional_skills: List[str] = Field(default_factory=list)
    academic_skills: List[str] = Field(default_factory=list)
    certification_skills: List[str] = Field(default_factory=list)
    recent_skills: List[str] = Field(default_factory=list)
    outdated_skills: List[str] = Field(default_factory=list)
    skill_timeline: Dict[str, List[str]] = Field(default_factory=dict)  # skill -> positions
    skill_classifications: List[SkillClassification] = Field(default_factory=list)


class JobStabilityAnalysis(BaseModel):
    """Analysis of job stability and career progression."""
    average_position_duration_months: float = 0.0
    total_positions: int = 0
    short_positions_count: int = 0  # Positions < 6 months
    job_hopping_score: float = Field(ge=0.0, le=1.0, default=0.0)  # Higher = more job hopping
    stability_score: float = Field(ge=0.0, le=1.0, default=0.5)  # Higher = more stable
    career_progression_trend: str = "stable"  # "upward", "downward", "stable", "unclear"


class RedFlag(BaseModel):
    """Represents a red flag identified during analysis."""
    type: RedFlagType
    severity: Severity
    description: str
    details: Dict[str, Any] = Field(default_factory=dict)
    position_related: Optional[str] = None  # Position title if related to specific position
    
    def __str__(self) -> str:
        return f"{self.severity.upper()}: {self.description}"


class ExperienceTimeline(BaseModel):
    """Timeline representation of candidate's experience."""
    total_experience_months: int = 0
    positions: List[Position] = Field(default_factory=list)
    gaps: List[EmploymentGap] = Field(default_factory=list)
    overlapping_positions: List[tuple[str, str]] = Field(default_factory=list)  # (pos1, pos2)


class AnalysisConfig(BaseModel):
    """Configuration parameters for CV analysis."""
    # Gap detection settings
    gap_threshold_months: int = Field(default=3, ge=1, le=12)
    
    # Job stability settings
    short_position_threshold_months: int = Field(default=6, ge=1, le=24)
    job_hopping_threshold: int = Field(default=3, ge=2, le=10)  # Number of short positions
    
    # Skill analysis settings
    recent_experience_years: int = Field(default=5, ge=1, le=15)
    
    # Scoring weights
    skill_weights: Dict[str, float] = Field(default_factory=lambda: {
        "professional": 1.0,
        "academic": 0.3,
        "certification": 0.8,
        "personal_project": 0.5
    })
    
    # Job requirements
    mandatory_skills: List[str] = Field(default_factory=list)
    preferred_skills: List[str] = Field(default_factory=list)
    
    # LLM settings
    llm_temperature: float = Field(default=0.7, ge=0.0, le=2.0)
    llm_max_tokens: int = Field(default=10000, ge=1000, le=50000)
    
    # Processing settings
    max_retries: int = Field(default=3, ge=1, le=10)
    timeout_seconds: int = Field(default=300, ge=30, le=1800)


# Original CandidateAssessment model (for backward compatibility)
class CandidateAssessment(BaseModel):
    """Original candidate assessment model."""
    candidate_name: Optional[str] = None
    contact_phone: Optional[str] = None
    address: Optional[str] = None
    email: Optional[str] = None
    github: Optional[str] = None
    linkedin: Optional[str] = None
    age: Optional[int] = None
    current_position: Optional[str] = None
    match_score: float = Field(ge=0.0, le=1.0)
    matched_sections: List[str] = Field(default_factory=list)
    experience_years: int = 0
    certifications: List[str] = Field(default_factory=list)
    summary: str = ""


# Enhanced assessment model extending the original
class EnhancedAssessment(CandidateAssessment):
    """Enhanced candidate assessment with advanced analysis features."""
    
    # Enhanced career analysis
    positions: List[Position] = Field(default_factory=list)
    employment_gaps: List[EmploymentGap] = Field(default_factory=list)
    job_stability_analysis: Optional[JobStabilityAnalysis] = None
    skill_analysis: Optional[SkillAnalysis] = None
    experience_timeline: Optional[ExperienceTimeline] = None
    
    # Red flags and issues
    red_flags: List[RedFlag] = Field(default_factory=list)
    
    # Processing metadata
    processing_status: ProcessingStatus = ProcessingStatus.PENDING
    analysis_timestamp: Optional[datetime] = None
    processing_time_seconds: Optional[float] = None
    error_messages: List[str] = Field(default_factory=list)
    
    # Enhanced scoring
    detailed_match_scores: Dict[str, float] = Field(default_factory=dict)  # skill category -> score
    confidence_score: float = Field(ge=0.0, le=1.0, default=0.5)  # Confidence in the analysis
    
    def add_red_flag(self, flag_type: RedFlagType, severity: Severity, description: str, 
                     details: Optional[Dict[str, Any]] = None, position_related: Optional[str] = None):
        """Add a red flag to the assessment."""
        red_flag = RedFlag(
            type=flag_type,
            severity=severity,
            description=description,
            details=details or {},
            position_related=position_related
        )
        self.red_flags.append(red_flag)
    
    def get_red_flags_by_severity(self, severity: Severity) -> List[RedFlag]:
        """Get all red flags of a specific severity."""
        return [flag for flag in self.red_flags if flag.severity == severity]
    
    def get_critical_red_flags(self) -> List[RedFlag]:
        """Get all critical red flags."""
        return self.get_red_flags_by_severity(Severity.CRITICAL)
    
    def calculate_overall_risk_score(self) -> float:
        """Calculate an overall risk score based on red flags."""
        if not self.red_flags:
            return 0.0
        
        severity_weights = {
            Severity.LOW: 0.1,
            Severity.MEDIUM: 0.3,
            Severity.HIGH: 0.7,
            Severity.CRITICAL: 1.0
        }
        
        total_weight = sum(severity_weights[flag.severity] for flag in self.red_flags)
        return min(1.0, total_weight / len(self.red_flags))
    
    @classmethod
    def from_basic_assessment(cls, basic: CandidateAssessment) -> "EnhancedAssessment":
        """Create an enhanced assessment from a basic assessment."""
        return cls(**basic.model_dump())


# Batch processing models
class BatchProcessingResult(BaseModel):
    """Result of batch CV processing."""
    assessments: List[EnhancedAssessment] = Field(default_factory=list)
    total_processed: int = 0
    successful_count: int = 0
    failed_count: int = 0
    processing_time_seconds: float = 0.0
    errors: List[str] = Field(default_factory=list)
    
    def add_assessment(self, assessment: EnhancedAssessment):
        """Add an assessment to the batch result."""
        self.assessments.append(assessment)
        self.total_processed += 1
        
        if assessment.processing_status == ProcessingStatus.COMPLETED:
            self.successful_count += 1
        else:
            self.failed_count += 1
            if assessment.error_messages:
                self.errors.extend(assessment.error_messages)


# Export models
class ExportConfig(BaseModel):
    """Configuration for exporting analysis results."""
    include_red_flags: bool = True
    include_skill_analysis: bool = True
    include_position_details: bool = True
    include_processing_metadata: bool = False
    format: str = Field(default="excel", pattern="^(excel|csv|json)$")
    filename_prefix: str = "cv_analysis"