["test_models.py::TestAnalysisConfig::test_analysis_config_custom_values", "test_models.py::TestAnalysisConfig::test_analysis_config_defaults", "test_models.py::TestAnalysisConfig::test_analysis_config_validation", "test_models.py::TestBatchProcessingResult::test_add_assessment_failed", "test_models.py::TestBatchProcessingResult::test_add_assessment_successful", "test_models.py::TestBatchProcessingResult::test_batch_processing_result_creation", "test_models.py::TestCandidateAssessment::test_candidate_assessment_creation", "test_models.py::TestCandidateAssessment::test_candidate_assessment_defaults", "test_models.py::TestCandidateAssessment::test_match_score_validation", "test_models.py::TestEmploymentGap::test_employment_gap_creation", "test_models.py::TestEmploymentGap::test_from_positions_no_gap", "test_models.py::TestEmploymentGap::test_from_positions_overlapping", "test_models.py::TestEmploymentGap::test_from_positions_with_gap", "test_models.py::TestEmploymentGap::test_gap_severity_classification", "test_models.py::TestEnhancedAssessment::test_add_red_flag", "test_models.py::TestEnhancedAssessment::test_calculate_overall_risk_score", "test_models.py::TestEnhancedAssessment::test_enhanced_assessment_creation", "test_models.py::TestEnhancedAssessment::test_from_basic_assessment", "test_models.py::TestEnhancedAssessment::test_get_red_flags_by_severity", "test_models.py::TestEnums::test_gap_severity_enum", "test_models.py::TestEnums::test_processing_status_enum", "test_models.py::TestEnums::test_red_flag_type_enum", "test_models.py::TestEnums::test_severity_enum", "test_models.py::TestEnums::test_skill_context_enum", "test_models.py::TestExportConfig::test_export_config_custom_values", "test_models.py::TestExportConfig::test_export_config_defaults", "test_models.py::TestExportConfig::test_export_config_format_validation", "test_models.py::TestModelSerialization::test_analysis_config_serialization", "test_models.py::TestModelSerialization::test_enhanced_assessment_serialization", "test_models.py::TestPosition::test_calculate_duration", "test_models.py::TestPosition::test_calculate_duration_current_position", "test_models.py::TestPosition::test_calculate_duration_no_start_date", "test_models.py::TestPosition::test_position_creation", "test_models.py::TestPosition::test_position_with_dates", "test_models.py::TestRedFlag::test_red_flag_creation", "test_models.py::TestRedFlag::test_red_flag_str_representation", "test_models.py::TestSkillClassification::test_skill_classification_creation", "test_models.py::TestSkillClassification::test_skill_classification_defaults"]