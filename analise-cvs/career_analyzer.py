"""
Career Analysis Engine for CV Analyzer Enhancement.

This module implements advanced career analysis features including employment gap detection,
job stability analysis, and skill context classification.
"""

from datetime import datetime, timedelta
from typing import List, Optional, Dict, Tuple
import logging
from models import (
    Position, EmploymentGap, GapSeverity, JobStabilityAnalysis, 
    SkillAnalysis, SkillClassification, SkillContext, ExperienceTimeline,
    RedFlag, RedFlagType, Severity, AnalysisConfig
)

logger = logging.getLogger(__name__)


class CareerAnalyzer:
    """Main class for analyzing career patterns and detecting red flags."""
    
    def __init__(self, config: AnalysisConfig):
        """Initialize the career analyzer with configuration."""
        self.config = config
    
    def detect_employment_gaps(self, positions: List[Position]) -> List[EmploymentGap]:
        """
        Detect employment gaps between consecutive positions.
        
        Args:
            positions: List of positions sorted by start date
            
        Returns:
            List of detected employment gaps
        """
        if len(positions) < 2:
            return []
        
        gaps = []
        
        # Sort positions by start date to ensure proper ordering
        sorted_positions = self._sort_positions_by_date(positions)
        
        for i in range(len(sorted_positions) - 1):
            current_pos = sorted_positions[i]
            next_pos = sorted_positions[i + 1]
            
            # Skip if either position lacks required dates
            if not current_pos.end_date or not next_pos.start_date:
                logger.warning(f"Missing dates for gap detection between {current_pos.company} and {next_pos.company}")
                continue
            
            # Handle overlapping positions
            if current_pos.end_date > next_pos.start_date:
                logger.info(f"Overlapping positions detected: {current_pos.company} and {next_pos.company}")
                continue
            
            # Calculate gap duration
            gap_duration = next_pos.start_date - current_pos.end_date
            gap_months = int(gap_duration.days / 30.44)  # Average days per month
            
            # Only consider gaps above the threshold
            if gap_months >= self.config.gap_threshold_months:
                gap = self._create_employment_gap(current_pos, next_pos, gap_months)
                if gap:
                    gaps.append(gap)
                    logger.info(f"Employment gap detected: {gap_months} months between {current_pos.company} and {next_pos.company}")
        
        return gaps
    
    def _sort_positions_by_date(self, positions: List[Position]) -> List[Position]:
        """Sort positions by start date, handling missing dates."""
        def sort_key(pos: Position) -> datetime:
            if pos.start_date:
                return pos.start_date
            # If no start date, put at the end
            return datetime.max
        
        return sorted(positions, key=sort_key)
    
    def _create_employment_gap(self, prev_pos: Position, next_pos: Position, gap_months: int) -> Optional[EmploymentGap]:
        """Create an employment gap object with appropriate severity."""
        if not prev_pos.end_date or not next_pos.start_date:
            return None
        
        # Determine severity based on gap duration
        severity = self._calculate_gap_severity(gap_months)
        
        return EmploymentGap(
            start_date=prev_pos.end_date,
            end_date=next_pos.start_date,
            duration_months=gap_months,
            severity=severity,
            description=f"{gap_months}-month gap between {prev_pos.company} ({prev_pos.title}) and {next_pos.company} ({next_pos.title})"
        )
    
    def _calculate_gap_severity(self, gap_months: int) -> GapSeverity:
        """Calculate gap severity based on duration."""
        if gap_months < 6:
            return GapSeverity.MINOR
        elif gap_months < 12:
            return GapSeverity.MODERATE
        elif gap_months < 24:
            return GapSeverity.SIGNIFICANT
        else:
            return GapSeverity.MAJOR
    
    def analyze_job_stability(self, positions: List[Position]) -> JobStabilityAnalysis:
        """
        Analyze job stability patterns including job hopping detection.
        
        Args:
            positions: List of candidate positions
            
        Returns:
            JobStabilityAnalysis with stability metrics
        """
        if not positions:
            return JobStabilityAnalysis()
        
        # Calculate position durations
        durations = []
        short_positions = 0
        
        for position in positions:
            duration = position.calculate_duration()
            if duration is not None:
                durations.append(duration)
                if duration < self.config.short_position_threshold_months:
                    short_positions += 1
        
        # Calculate average duration
        avg_duration = sum(durations) / len(durations) if durations else 0.0
        
        # Calculate job hopping score (0-1, higher = more job hopping)
        job_hopping_score = min(1.0, short_positions / max(1, len(positions)))
        
        # Calculate stability score (inverse of job hopping, adjusted for average duration)
        stability_score = self._calculate_stability_score(avg_duration, job_hopping_score, len(positions))
        
        # Determine career progression trend
        progression_trend = self._analyze_career_progression(positions)
        
        return JobStabilityAnalysis(
            average_position_duration_months=avg_duration,
            total_positions=len(positions),
            short_positions_count=short_positions,
            job_hopping_score=job_hopping_score,
            stability_score=stability_score,
            career_progression_trend=progression_trend
        )
    
    def _calculate_stability_score(self, avg_duration: float, job_hopping_score: float, total_positions: int) -> float:
        """Calculate overall job stability score."""
        # Base score from average duration (normalized to 0-1)
        duration_score = min(1.0, avg_duration / 36.0)  # 36 months = perfect score
        
        # Penalty for job hopping
        hopping_penalty = job_hopping_score * 0.5
        
        # Bonus for having reasonable number of positions (not too few, not too many)
        position_factor = 1.0
        if total_positions < 2:
            position_factor = 0.7  # Too few positions to assess
        elif total_positions > 8:
            position_factor = 0.9  # Many positions might indicate instability
        
        stability_score = (duration_score - hopping_penalty) * position_factor
        return max(0.0, min(1.0, stability_score))
    
    def _analyze_career_progression(self, positions: List[Position]) -> str:
        """Analyze career progression trend based on position titles and companies."""
        if len(positions) < 2:
            return "unclear"
        
        # Sort positions by start date
        sorted_positions = self._sort_positions_by_date(positions)
        
        # Simple heuristic: look for keywords that indicate seniority
        seniority_keywords = {
            'junior': 1, 'associate': 2, 'mid': 3, 'senior': 4, 
            'lead': 5, 'principal': 6, 'manager': 5, 'director': 7, 
            'vp': 8, 'vice president': 8, 'cto': 9, 'ceo': 10
        }
        
        scores = []
        for position in sorted_positions:
            title_lower = position.title.lower()
            score = 3  # Default mid-level score
            
            for keyword, value in seniority_keywords.items():
                if keyword in title_lower:
                    score = value
                    break
            
            scores.append(score)
        
        if len(scores) < 2:
            return "unclear"
        
        # Analyze trend
        first_half_avg = sum(scores[:len(scores)//2]) / (len(scores)//2)
        second_half_avg = sum(scores[len(scores)//2:]) / (len(scores) - len(scores)//2)
        
        if second_half_avg > first_half_avg + 0.5:
            return "upward"
        elif second_half_avg < first_half_avg - 0.5:
            return "downward"
        else:
            return "stable"
    
    def build_experience_timeline(self, positions: List[Position]) -> ExperienceTimeline:
        """
        Build a comprehensive experience timeline with gaps and overlaps.
        
        Args:
            positions: List of candidate positions
            
        Returns:
            ExperienceTimeline object
        """
        if not positions:
            return ExperienceTimeline()
        
        # Sort positions by start date
        sorted_positions = self._sort_positions_by_date(positions)
        
        # Detect employment gaps
        gaps = self.detect_employment_gaps(sorted_positions)
        
        # Detect overlapping positions
        overlaps = self._detect_overlapping_positions(sorted_positions)
        
        # Calculate total experience
        total_experience = self._calculate_total_experience(sorted_positions)
        
        return ExperienceTimeline(
            total_experience_months=total_experience,
            positions=sorted_positions,
            gaps=gaps,
            overlapping_positions=overlaps
        )
    
    def _detect_overlapping_positions(self, positions: List[Position]) -> List[Tuple[str, str]]:
        """Detect positions that overlap in time."""
        overlaps = []
        
        for i in range(len(positions)):
            for j in range(i + 1, len(positions)):
                pos1, pos2 = positions[i], positions[j]
                
                if not all([pos1.start_date, pos1.end_date, pos2.start_date, pos2.end_date]):
                    continue
                
                # Check for overlap
                if (pos1.start_date <= pos2.end_date and pos1.end_date >= pos2.start_date):
                    overlap_desc = f"{pos1.title} at {pos1.company}"
                    overlap_desc2 = f"{pos2.title} at {pos2.company}"
                    overlaps.append((overlap_desc, overlap_desc2))
        
        return overlaps
    
    def _calculate_total_experience(self, positions: List[Position]) -> int:
        """Calculate total work experience in months, accounting for overlaps."""
        if not positions:
            return 0
        
        # Create timeline of employment periods
        periods = []
        for position in positions:
            if position.start_date:
                end_date = position.end_date or datetime.now()
                periods.append((position.start_date, end_date))
        
        if not periods:
            return 0
        
        # Sort periods by start date
        periods.sort(key=lambda x: x[0])
        
        # Merge overlapping periods
        merged_periods = []
        current_start, current_end = periods[0]
        
        for start, end in periods[1:]:
            if start <= current_end:
                # Overlapping period, extend current period
                current_end = max(current_end, end)
            else:
                # Non-overlapping period, save current and start new
                merged_periods.append((current_start, current_end))
                current_start, current_end = start, end
        
        # Add the last period
        merged_periods.append((current_start, current_end))
        
        # Calculate total duration
        total_days = sum((end - start).days for start, end in merged_periods)
        return int(total_days / 30.44)  # Convert to months
    
    def generate_career_red_flags(self, positions: List[Position], gaps: List[EmploymentGap], 
                                stability_analysis: JobStabilityAnalysis) -> List[RedFlag]:
        """
        Generate red flags based on career analysis results.
        
        Args:
            positions: List of positions
            gaps: List of employment gaps
            stability_analysis: Job stability analysis results
            
        Returns:
            List of red flags
        """
        red_flags = []
        
        # Employment gap red flags
        for gap in gaps:
            severity = self._gap_severity_to_red_flag_severity(gap.severity)
            red_flags.append(RedFlag(
                type=RedFlagType.EMPLOYMENT_GAP,
                severity=severity,
                description=f"Employment gap of {gap.duration_months} months",
                details={
                    "duration_months": gap.duration_months,
                    "start_date": gap.start_date.isoformat(),
                    "end_date": gap.end_date.isoformat(),
                    "gap_severity": gap.severity.value
                }
            ))
        
        # Job hopping red flags
        if stability_analysis.short_positions_count >= self.config.job_hopping_threshold:
            red_flags.append(RedFlag(
                type=RedFlagType.JOB_HOPPING,
                severity=Severity.MEDIUM if stability_analysis.short_positions_count < 5 else Severity.HIGH,
                description=f"Job hopping pattern: {stability_analysis.short_positions_count} positions under {self.config.short_position_threshold_months} months",
                details={
                    "short_positions_count": stability_analysis.short_positions_count,
                    "job_hopping_score": stability_analysis.job_hopping_score,
                    "threshold": self.config.job_hopping_threshold
                }
            ))
        
        # Short position red flags
        for position in positions:
            duration = position.calculate_duration()
            if duration and duration < self.config.short_position_threshold_months and not position.is_current:
                red_flags.append(RedFlag(
                    type=RedFlagType.SHORT_POSITION,
                    severity=Severity.LOW if duration >= 3 else Severity.MEDIUM,
                    description=f"Short position: {position.title} at {position.company} ({duration} months)",
                    details={
                        "position_title": position.title,
                        "company": position.company,
                        "duration_months": duration
                    },
                    position_related=position.title
                ))
        
        return red_flags
    
    def _gap_severity_to_red_flag_severity(self, gap_severity: GapSeverity) -> Severity:
        """Convert gap severity to red flag severity."""
        mapping = {
            GapSeverity.MINOR: Severity.LOW,
            GapSeverity.MODERATE: Severity.MEDIUM,
            GapSeverity.SIGNIFICANT: Severity.HIGH,
            GapSeverity.MAJOR: Severity.CRITICAL
        }
        return mapping.get(gap_severity, Severity.MEDIUM)
    
    def classify_skill_context(self, skills: List[str], cv_text: str, positions: List[Position]) -> Dict[str, SkillClassification]:
        """
        Classify skills by their context (academic, professional, certification).
        
        Args:
            skills: List of skills to classify
            cv_text: Full CV text for context analysis
            positions: List of positions for skill-to-position mapping
            
        Returns:
            Dictionary mapping skill names to their classifications
        """
        classifications = {}
        
        # Analyze CV sections first
        cv_sections = self._analyze_cv_sections(cv_text)
        
        for skill in skills:
            classification = self._classify_single_skill_enhanced(skill, cv_text, cv_sections, positions)
            classifications[skill] = classification
        
        return classifications
    
    def _analyze_cv_sections(self, cv_text: str) -> Dict[str, Dict[str, any]]:
        """
        Analyze CV text to identify different sections and their boundaries.
        
        Args:
            cv_text: Full CV text
            
        Returns:
            Dictionary with section information including boundaries and keywords
        """
        cv_text_lower = cv_text.lower()
        sections = {}
        
        # Define section headers and their associated keywords
        section_patterns = {
            'education': {
                'headers': ['education', 'academic background', 'qualifications', 'degrees'],
                'keywords': [
                    'university', 'college', 'degree', 'bachelor', 'master', 'phd', 
                    'thesis', 'dissertation', 'coursework', 'academic', 'school',
                    'student', 'graduation', 'semester', 'course', 'gpa', 'honors'
                ],
                'weight': 1.0
            },
            'experience': {
                'headers': ['experience', 'work experience', 'employment', 'career', 'professional experience'],
                'keywords': [
                    'company', 'corporation', 'organization', 'team', 'project',
                    'developed', 'implemented', 'managed', 'led', 'created', 'built',
                    'responsible', 'achieved', 'delivered', 'collaborated', 'worked'
                ],
                'weight': 1.2
            },
            'certifications': {
                'headers': ['certifications', 'certificates', 'licenses', 'credentials'],
                'keywords': [
                    'certification', 'certified', 'certificate', 'license', 'credential',
                    'exam', 'test', 'qualified', 'accredited', 'endorsed', 'verified',
                    'aws', 'microsoft', 'cisco', 'oracle', 'google cloud'
                ],
                'weight': 1.1
            },
            'skills': {
                'headers': ['skills', 'technical skills', 'technologies', 'tools'],
                'keywords': [
                    'programming', 'languages', 'frameworks', 'tools', 'technologies',
                    'software', 'platforms', 'databases', 'operating systems'
                ],
                'weight': 0.8
            },
            'projects': {
                'headers': ['projects', 'personal projects', 'side projects', 'portfolio'],
                'keywords': [
                    'project', 'built', 'created', 'developed', 'github', 'repository',
                    'demo', 'prototype', 'application', 'website', 'app'
                ],
                'weight': 0.9
            }
        }
        
        # Find section boundaries
        for section_name, section_info in section_patterns.items():
            section_data = self._find_section_boundaries(cv_text_lower, section_info)
            if section_data:
                sections[section_name] = section_data
        
        return sections
    
    def _find_section_boundaries(self, cv_text_lower: str, section_info: Dict) -> Optional[Dict]:
        """Find the boundaries of a specific section in the CV."""
        # Look for section headers
        header_positions = []
        for header in section_info['headers']:
            pos = cv_text_lower.find(header)
            if pos != -1:
                # Check if it's likely a section header (at start of line, followed by colon, etc.)
                if self._is_likely_section_header(cv_text_lower, pos, header):
                    header_positions.append(pos)
        
        if not header_positions:
            return None
        
        # Use the first found header as section start
        section_start = min(header_positions)
        
        # Find section end (next major section or end of document)
        section_end = self._find_section_end(cv_text_lower, section_start)
        
        # Calculate keyword density in this section
        section_text = cv_text_lower[section_start:section_end]
        keyword_count = sum(1 for keyword in section_info['keywords'] if keyword in section_text)
        keyword_density = keyword_count / len(section_info['keywords']) if section_info['keywords'] else 0
        
        return {
            'start': section_start,
            'end': section_end,
            'text': section_text,
            'keyword_density': keyword_density,
            'weight': section_info['weight']
        }
    
    def _is_likely_section_header(self, text: str, pos: int, header: str) -> bool:
        """Check if the found text is likely a section header."""
        # Check if it's at the beginning of a line
        line_start = text.rfind('\n', 0, pos)
        if line_start == -1:
            line_start = 0
        else:
            line_start += 1
        
        # Check if there's minimal text before the header on the same line
        prefix = text[line_start:pos].strip()
        if len(prefix) > 10:  # Too much text before, probably not a header
            return False
        
        # Check if followed by colon, newline, or similar
        end_pos = pos + len(header)
        if end_pos < len(text):
            next_chars = text[end_pos:end_pos + 3]
            if any(char in next_chars for char in [':', '\n', '-', '=']):
                return True
        
        return True
    
    def _find_section_end(self, text: str, section_start: int) -> int:
        """Find where a section ends by looking for the next major section."""
        # Common section headers that would indicate end of current section
        major_headers = [
            'education', 'experience', 'skills', 'certifications', 'projects',
            'work experience', 'employment', 'qualifications', 'achievements'
        ]
        
        next_section_pos = len(text)  # Default to end of document
        
        for header in major_headers:
            pos = text.find(header, section_start + 50)  # Look after current section start
            if pos != -1 and self._is_likely_section_header(text, pos, header):
                next_section_pos = min(next_section_pos, pos)
        
        return next_section_pos
    
    def _classify_single_skill_enhanced(self, skill: str, cv_text: str, cv_sections: Dict, positions: List[Position]) -> SkillClassification:
        """Enhanced single skill classification using section analysis."""
        skill_lower = skill.lower()
        cv_text_lower = cv_text.lower()
        
        # Find all mentions of the skill
        skill_mentions = self._find_skill_mentions(skill_lower, cv_text_lower)
        
        if not skill_mentions:
            return SkillClassification(
                skill=skill,
                context=SkillContext.UNCLEAR,
                confidence=0.1
            )
        
        # Analyze context for each mention using section information
        context_scores = {
            SkillContext.ACADEMIC: 0.0,
            SkillContext.PROFESSIONAL: 0.0,
            SkillContext.CERTIFICATION: 0.0,
            SkillContext.PERSONAL_PROJECT: 0.0
        }
        
        total_weight = 0.0
        
        for mention_start, mention_end in skill_mentions:
            # Determine which section this mention belongs to
            section_context = self._determine_mention_section(mention_start, cv_sections)
            
            if section_context:
                section_name, section_weight = section_context
                
                # Map section to skill context
                if section_name == 'education':
                    context_scores[SkillContext.ACADEMIC] += section_weight
                elif section_name == 'experience':
                    context_scores[SkillContext.PROFESSIONAL] += section_weight
                elif section_name == 'certifications':
                    context_scores[SkillContext.CERTIFICATION] += section_weight
                elif section_name == 'projects':
                    context_scores[SkillContext.PERSONAL_PROJECT] += section_weight
                else:
                    # Default scoring based on surrounding context
                    context_weight = self._analyze_mention_context(mention_start, mention_end, cv_text_lower)
                    for context, weight in context_weight.items():
                        context_scores[context] += weight
                
                total_weight += section_weight
            else:
                # Fallback to traditional context analysis
                context_weight = self._analyze_mention_context(mention_start, mention_end, cv_text_lower)
                for context, weight in context_weight.items():
                    context_scores[context] += weight
                total_weight += 1.0
        
        # Normalize scores
        if total_weight > 0:
            for context in context_scores:
                context_scores[context] /= total_weight
        
        # Determine primary context
        primary_context = max(context_scores.keys(), key=lambda k: context_scores[k])
        confidence = context_scores[primary_context]
        
        # Boost confidence if skill appears in multiple professional contexts
        if primary_context == SkillContext.PROFESSIONAL:
            professional_mentions = self._count_professional_mentions(skill, positions)
            if professional_mentions > 1:
                confidence = min(1.0, confidence * 1.2)
        
        # Check if skill is used in recent positions
        is_recent = self._is_skill_recent(skill, positions)
        
        # Map skill to positions where it's used
        positions_used = self._map_skill_to_positions(skill, positions)
        
        return SkillClassification(
            skill=skill,
            context=primary_context,
            is_recent=is_recent,
            positions_used=positions_used,
            confidence=min(1.0, confidence)
        )
    
    def _determine_mention_section(self, mention_pos: int, cv_sections: Dict) -> Optional[Tuple[str, float]]:
        """Determine which CV section a skill mention belongs to."""
        for section_name, section_data in cv_sections.items():
            if section_data['start'] <= mention_pos <= section_data['end']:
                return section_name, section_data['weight']
        return None
    
    def _analyze_mention_context(self, mention_start: int, mention_end: int, cv_text_lower: str) -> Dict[SkillContext, float]:
        """Analyze the context around a skill mention using traditional keyword matching."""
        # Get context window around the skill mention
        context_start = max(0, mention_start - 200)
        context_end = min(len(cv_text_lower), mention_end + 200)
        context = cv_text_lower[context_start:context_end]
        
        # Define context keywords
        context_keywords = {
            SkillContext.ACADEMIC: [
                'university', 'college', 'degree', 'bachelor', 'master', 'phd',
                'thesis', 'dissertation', 'coursework', 'academic', 'school',
                'student', 'graduation', 'semester', 'course', 'project', 'assignment'
            ],
            SkillContext.PROFESSIONAL: [
                'experience', 'work', 'employment', 'job', 'position', 'role',
                'company', 'corporation', 'organization', 'team', 'project',
                'developed', 'implemented', 'managed', 'led', 'created', 'built',
                'responsible', 'achieved', 'delivered', 'collaborated'
            ],
            SkillContext.CERTIFICATION: [
                'certification', 'certified', 'certificate', 'license', 'credential',
                'exam', 'test', 'qualified', 'accredited', 'endorsed', 'verified'
            ],
            SkillContext.PERSONAL_PROJECT: [
                'personal', 'side project', 'hobby', 'github', 'portfolio',
                'built for fun', 'learning', 'self-taught', 'tutorial'
            ]
        }
        
        # Score context based on keyword presence
        context_scores = {}
        for context_type, keywords in context_keywords.items():
            score = sum(1 for keyword in keywords if keyword in context)
            context_scores[context_type] = score
        
        # Normalize scores
        total_score = sum(context_scores.values())
        if total_score > 0:
            for context_type in context_scores:
                context_scores[context_type] /= total_score
        
        return context_scores
    
    def _count_professional_mentions(self, skill: str, positions: List[Position]) -> int:
        """Count how many professional positions mention this skill."""
        skill_lower = skill.lower()
        count = 0
        
        for position in positions:
            position_text = f"{position.title} {position.description}".lower()
            if skill_lower in position_text:
                count += 1
        
        return count
    
    def _classify_single_skill(self, skill: str, skill_lower: str, cv_text_lower: str, 
                              positions: List[Position], academic_keywords: List[str],
                              professional_keywords: List[str], certification_keywords: List[str]) -> SkillClassification:
        """Classify a single skill based on context analysis."""
        
        # Find all mentions of the skill in the CV
        skill_mentions = self._find_skill_mentions(skill_lower, cv_text_lower)
        
        if not skill_mentions:
            return SkillClassification(
                skill=skill,
                context=SkillContext.UNCLEAR,
                confidence=0.1
            )
        
        # Analyze context around each mention
        context_scores = {
            SkillContext.ACADEMIC: 0.0,
            SkillContext.PROFESSIONAL: 0.0,
            SkillContext.CERTIFICATION: 0.0
        }
        
        for mention_start, mention_end in skill_mentions:
            # Get context window around the skill mention
            context_start = max(0, mention_start - 200)
            context_end = min(len(cv_text_lower), mention_end + 200)
            context = cv_text_lower[context_start:context_end]
            
            # Score context based on keyword presence
            academic_score = sum(1 for keyword in academic_keywords if keyword in context)
            professional_score = sum(1 for keyword in professional_keywords if keyword in context)
            certification_score = sum(1 for keyword in certification_keywords if keyword in context)
            
            # Normalize scores
            total_score = academic_score + professional_score + certification_score
            if total_score > 0:
                context_scores[SkillContext.ACADEMIC] += academic_score / total_score
                context_scores[SkillContext.PROFESSIONAL] += professional_score / total_score
                context_scores[SkillContext.CERTIFICATION] += certification_score / total_score
        
        # Determine primary context
        primary_context = max(context_scores.keys(), key=lambda k: context_scores[k])
        confidence = context_scores[primary_context] / len(skill_mentions) if skill_mentions else 0.0
        
        # Check if skill is used in recent positions
        is_recent = self._is_skill_recent(skill, positions)
        
        # Map skill to positions where it's used
        positions_used = self._map_skill_to_positions(skill, positions)
        
        return SkillClassification(
            skill=skill,
            context=primary_context,
            is_recent=is_recent,
            positions_used=positions_used,
            confidence=min(1.0, confidence)
        )
    
    def _find_skill_mentions(self, skill_lower: str, cv_text_lower: str) -> List[Tuple[int, int]]:
        """Find all mentions of a skill in the CV text."""
        mentions = []
        start = 0
        
        while True:
            pos = cv_text_lower.find(skill_lower, start)
            if pos == -1:
                break
            
            # Check if it's a whole word (not part of another word)
            if self._is_whole_word(skill_lower, cv_text_lower, pos):
                mentions.append((pos, pos + len(skill_lower)))
            
            start = pos + 1
        
        return mentions
    
    def _is_whole_word(self, skill: str, text: str, pos: int) -> bool:
        """Check if the skill at position is a whole word."""
        # Check character before
        if pos > 0 and text[pos - 1].isalnum():
            return False
        
        # Check character after
        end_pos = pos + len(skill)
        if end_pos < len(text) and text[end_pos].isalnum():
            return False
        
        return True
    
    def _is_skill_recent(self, skill: str, positions: List[Position]) -> bool:
        """Check if skill was used in recent positions (last 3 positions or 5 years)."""
        if not positions:
            return False
        
        # Sort positions by start date (most recent first)
        sorted_positions = sorted(
            [p for p in positions if p.start_date],
            key=lambda p: p.start_date,
            reverse=True
        )
        
        # Check last 3 positions
        recent_positions = sorted_positions[:3]
        
        # Also check positions within last 5 years
        five_years_ago = datetime.now() - timedelta(days=5*365)
        recent_by_date = [
            p for p in sorted_positions 
            if p.start_date and p.start_date >= five_years_ago
        ]
        
        # Combine both criteria (use list instead of set since Position is not hashable)
        all_recent = recent_positions + [p for p in recent_by_date if p not in recent_positions]
        
        # Check if skill is mentioned in any recent position
        skill_lower = skill.lower()
        for position in all_recent:
            position_text = f"{position.title} {position.description}".lower()
            if skill_lower in position_text:
                return True
        
        return False
    
    def _map_skill_to_positions(self, skill: str, positions: List[Position]) -> List[str]:
        """Map a skill to the positions where it was used."""
        skill_lower = skill.lower()
        positions_used = []
        
        for position in positions:
            if self._skill_used_in_position(skill_lower, position):
                positions_used.append(position.title)
        
        return positions_used
    
    def _skill_used_in_position(self, skill_lower: str, position: Position) -> bool:
        """Check if a skill was used in a specific position with enhanced matching."""
        # Check in position title and description
        position_text = f"{position.title} {position.description}".lower()
        
        # Direct match
        if skill_lower in position_text:
            return True
        
        # Check for skill variations and synonyms
        skill_variations = self._get_skill_variations(skill_lower)
        for variation in skill_variations:
            if variation in position_text:
                return True
        
        # Check in explicitly listed skills for this position
        if position.skills_used:
            position_skills_lower = [s.lower() for s in position.skills_used]
            if skill_lower in position_skills_lower:
                return True
            
            # Check variations in position skills
            for variation in skill_variations:
                if variation in position_skills_lower:
                    return True
        
        return False
    
    def _get_skill_variations(self, skill_lower: str) -> List[str]:
        """Get common variations and synonyms for a skill."""
        # Common skill variations and synonyms
        skill_synonyms = {
            'javascript': ['js', 'node.js', 'nodejs', 'ecmascript'],
            'python': ['py', 'python3', 'python2'],
            'java': ['jvm', 'openjdk'],
            'c++': ['cpp', 'c plus plus'],
            'c#': ['csharp', 'c sharp', '.net'],
            'sql': ['mysql', 'postgresql', 'sqlite', 'mssql'],
            'html': ['html5', 'markup'],
            'css': ['css3', 'stylesheets'],
            'react': ['reactjs', 'react.js'],
            'angular': ['angularjs', 'angular.js'],
            'vue': ['vuejs', 'vue.js'],
            'docker': ['containerization', 'containers'],
            'kubernetes': ['k8s', 'container orchestration'],
            'aws': ['amazon web services', 'amazon aws'],
            'gcp': ['google cloud platform', 'google cloud'],
            'azure': ['microsoft azure'],
            'git': ['version control', 'source control'],
            'linux': ['unix', 'ubuntu', 'centos', 'redhat'],
            'windows': ['microsoft windows', 'win'],
            'macos': ['mac os', 'osx', 'mac os x'],
            'api': ['rest api', 'restful', 'web api'],
            'database': ['db', 'databases', 'data storage'],
            'machine learning': ['ml', 'artificial intelligence', 'ai'],
            'data science': ['data analysis', 'analytics'],
            'devops': ['ci/cd', 'continuous integration', 'continuous deployment']
        }
        
        variations = [skill_lower]
        
        # Add direct synonyms
        if skill_lower in skill_synonyms:
            variations.extend(skill_synonyms[skill_lower])
        
        # Check if skill is a synonym of another skill
        for main_skill, synonyms in skill_synonyms.items():
            if skill_lower in synonyms:
                variations.append(main_skill)
                variations.extend([s for s in synonyms if s != skill_lower])
        
        # Add common variations (plurals, etc.)
        if not skill_lower.endswith('s'):
            variations.append(skill_lower + 's')
        if skill_lower.endswith('s') and len(skill_lower) > 3:
            variations.append(skill_lower[:-1])
        
        return list(set(variations))  # Remove duplicates
    
    def build_enhanced_skill_to_position_mapping(self, skills: List[str], positions: List[Position]) -> Dict[str, Dict[str, any]]:
        """
        Build enhanced mapping of skills to positions with additional metadata.
        
        Args:
            skills: List of skills to map
            positions: List of positions
            
        Returns:
            Dictionary with detailed skill-to-position mapping
        """
        skill_mapping = {}
        
        for skill in skills:
            skill_data = {
                'positions_used': [],
                'total_mentions': 0,
                'recent_usage': False,
                'usage_timeline': [],
                'proficiency_indicators': []
            }
            
            skill_lower = skill.lower()
            
            for position in positions:
                if self._skill_used_in_position(skill_lower, position):
                    position_info = {
                        'title': position.title,
                        'company': position.company,
                        'start_date': position.start_date.isoformat() if position.start_date else None,
                        'end_date': position.end_date.isoformat() if position.end_date else None,
                        'is_current': position.is_current,
                        'mention_count': self._count_skill_mentions_in_position(skill_lower, position),
                        'context_strength': self._assess_skill_context_strength(skill_lower, position)
                    }
                    
                    skill_data['positions_used'].append(position_info)
                    skill_data['total_mentions'] += position_info['mention_count']
                    
                    # Check if this is recent usage
                    if position.is_current or self._is_position_recent(position):
                        skill_data['recent_usage'] = True
                    
                    # Add to timeline
                    if position.start_date:
                        skill_data['usage_timeline'].append({
                            'date': position.start_date.isoformat(),
                            'position': position.title,
                            'type': 'start'
                        })
                    
                    # Extract proficiency indicators
                    proficiency = self._extract_proficiency_indicators(skill_lower, position)
                    if proficiency:
                        skill_data['proficiency_indicators'].extend(proficiency)
            
            # Sort timeline by date
            skill_data['usage_timeline'].sort(key=lambda x: x['date'] if x['date'] else '')
            
            skill_mapping[skill] = skill_data
        
        return skill_mapping
    
    def _count_skill_mentions_in_position(self, skill_lower: str, position: Position) -> int:
        """Count how many times a skill is mentioned in a position."""
        position_text = f"{position.title} {position.description}".lower()
        count = position_text.count(skill_lower)
        
        # Also count variations
        variations = self._get_skill_variations(skill_lower)
        for variation in variations:
            if variation != skill_lower:
                count += position_text.count(variation)
        
        return count
    
    def _assess_skill_context_strength(self, skill_lower: str, position: Position) -> float:
        """Assess how strongly a skill is associated with a position (0-1 score)."""
        position_text = f"{position.title} {position.description}".lower()
        
        # Base score from mentions
        mention_count = self._count_skill_mentions_in_position(skill_lower, position)
        base_score = min(1.0, mention_count * 0.2)
        
        # Bonus for being in title
        title_bonus = 0.3 if skill_lower in position.title.lower() else 0.0
        
        # Bonus for action words around the skill
        action_words = ['developed', 'implemented', 'built', 'created', 'designed', 'managed', 'led']
        action_bonus = 0.0
        for action in action_words:
            if f"{action} {skill_lower}" in position_text or f"{skill_lower} {action}" in position_text:
                action_bonus += 0.1
        
        action_bonus = min(0.3, action_bonus)
        
        # Bonus for being explicitly listed in skills
        explicit_bonus = 0.2 if position.skills_used and skill_lower in [s.lower() for s in position.skills_used] else 0.0
        
        total_score = base_score + title_bonus + action_bonus + explicit_bonus
        return min(1.0, total_score)
    
    def _is_position_recent(self, position: Position) -> bool:
        """Check if a position is considered recent (within last 5 years)."""
        if not position.start_date:
            return False
        
        five_years_ago = datetime.now() - timedelta(days=5*365)
        return position.start_date >= five_years_ago
    
    def _extract_proficiency_indicators(self, skill_lower: str, position: Position) -> List[str]:
        """Extract indicators of proficiency level for a skill in a position."""
        position_text = f"{position.title} {position.description}".lower()
        indicators = []
        
        # Look for proficiency keywords around the skill
        proficiency_patterns = {
            'expert': ['expert', 'advanced', 'senior', 'lead', 'architect'],
            'intermediate': ['experienced', 'proficient', 'skilled', 'competent'],
            'beginner': ['basic', 'fundamental', 'learning', 'introduction']
        }
        
        # Find skill mentions and check surrounding context
        skill_mentions = self._find_skill_mentions(skill_lower, position_text)
        
        for mention_start, mention_end in skill_mentions:
            # Check context around the mention
            context_start = max(0, mention_start - 50)
            context_end = min(len(position_text), mention_end + 50)
            context = position_text[context_start:context_end]
            
            for level, keywords in proficiency_patterns.items():
                for keyword in keywords:
                    if keyword in context:
                        indicators.append(f"{level}_{keyword}")
        
        return list(set(indicators))  # Remove duplicates
    
    def build_skill_analysis(self, skills: List[str], cv_text: str, positions: List[Position]) -> SkillAnalysis:
        """
        Build comprehensive skill analysis including context classification.
        
        Args:
            skills: List of skills to analyze
            cv_text: Full CV text
            positions: List of positions
            
        Returns:
            SkillAnalysis object with categorized skills
        """
        # Classify all skills
        skill_classifications = self.classify_skill_context(skills, cv_text, positions)
        
        # Categorize skills by context
        professional_skills = []
        academic_skills = []
        certification_skills = []
        recent_skills = []
        outdated_skills = []
        skill_timeline = {}
        
        for skill, classification in skill_classifications.items():
            # Categorize by context
            if classification.context == SkillContext.PROFESSIONAL:
                professional_skills.append(skill)
            elif classification.context == SkillContext.ACADEMIC:
                academic_skills.append(skill)
            elif classification.context == SkillContext.CERTIFICATION:
                certification_skills.append(skill)
            
            # Categorize by recency
            if classification.is_recent:
                recent_skills.append(skill)
            else:
                outdated_skills.append(skill)
            
            # Build skill timeline
            if classification.positions_used:
                skill_timeline[skill] = classification.positions_used
        
        return SkillAnalysis(
            professional_skills=professional_skills,
            academic_skills=academic_skills,
            certification_skills=certification_skills,
            recent_skills=recent_skills,
            outdated_skills=outdated_skills,
            skill_timeline=skill_timeline,
            skill_classifications=list(skill_classifications.values())
        )
    
    def generate_skill_red_flags(self, skill_analysis: SkillAnalysis, 
                               mandatory_skills: List[str], preferred_skills: List[str]) -> List[RedFlag]:
        """
        Generate red flags based on skill analysis.
        
        Args:
            skill_analysis: Skill analysis results
            mandatory_skills: List of mandatory skills for the job
            preferred_skills: List of preferred skills for the job
            
        Returns:
            List of skill-related red flags
        """
        red_flags = []
        
        # Check for missing mandatory skills
        all_candidate_skills = set(
            skill_analysis.professional_skills + 
            skill_analysis.academic_skills + 
            skill_analysis.certification_skills
        )
        
        for mandatory_skill in mandatory_skills:
            if mandatory_skill.lower() not in {s.lower() for s in all_candidate_skills}:
                red_flags.append(RedFlag(
                    type=RedFlagType.MISSING_REQUIRED_SKILL,
                    severity=Severity.HIGH,
                    description=f"Missing mandatory skill: {mandatory_skill}",
                    details={"skill": mandatory_skill, "skill_type": "mandatory"}
                ))
        
        # Check for academic-only skills among mandatory requirements
        for mandatory_skill in mandatory_skills:
            if (mandatory_skill.lower() in {s.lower() for s in skill_analysis.academic_skills} and
                mandatory_skill.lower() not in {s.lower() for s in skill_analysis.professional_skills}):
                red_flags.append(RedFlag(
                    type=RedFlagType.ACADEMIC_ONLY_SKILL,
                    severity=Severity.MEDIUM,
                    description=f"Mandatory skill '{mandatory_skill}' appears only in academic context",
                    details={"skill": mandatory_skill, "context": "academic_only"}
                ))
        
        # Check for outdated skills
        for skill in skill_analysis.outdated_skills:
            if skill.lower() in {s.lower() for s in mandatory_skills + preferred_skills}:
                red_flags.append(RedFlag(
                    type=RedFlagType.OUTDATED_SKILL,
                    severity=Severity.LOW,
                    description=f"Required skill '{skill}' may be outdated (not used recently)",
                    details={"skill": skill, "last_used": "more_than_5_years_ago"}
                ))
        
        return red_flags